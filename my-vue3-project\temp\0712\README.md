#### todo01
1. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
2. 新增需求：
  读音播放方面：
  1. 显示新单词时，播放单词读音；
  2. 单词拼写错误时，播放单词读音；
  听写逻辑方面：
  1. 单词拼写正确时，记录听写结果；
        await dictationStore.updateDictationRecord({
        wordId: Number(currentWord.value.id),
        status: 1,
        source: 'zntx',
        sourceFrom: 'zntx',
      })
  2. 单词拼写错误一次，记录记录听写结果，
        await dictationStore.updateDictationRecord({
        wordId: Number(currentWord.value.id),
        status: 0,
        source: 'zntx',
        sourceFrom: 'zntx',
      })
  3. 错误一次，再拼写正确，不发送 await dictationStore.updateDictationRecord 请求，其中 status 为 1 的post 请求；
  4. 即，每次单词只记录一个听写结果，首次听写正确才认为听写成功；



#### todo02
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
3. 新增需求：
  1. 参考智能记忆文件中，暂停有效时间 effectiveSeconds 的计算方式，在智能听写文件中，也计算暂停有效时间，并记录到 effectiveSeconds 中；
  2. 在智能听写文件中，判断单词输入错误后，会显示单词正确拼写；
    在输入听写单词的过程中，这一段阶段，隐藏单词正确拼写；

#### todo03
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
3. 新增需求：
  1. 听写文件的页面局部与 智能记忆界面保持相似，或者一致；
  2. 大小、分布等风格保持一致；

#### todo04
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
3. 智能默写文件 src\views\dcyx\learn-main\inteli-writing.vue
4. 新增需求：
  1. 智能默写文件的页面局部与 智能记忆界面保持相似，或者一致；
  2. 大小、分布等风格保持一致；
  3. 实现智能默写文件的功能，参考智能记忆和智能听写文件；
    1. 包括输入框、单词拼写错误时，显示单词正确拼写；
    2. 包括有效时间更新、暂停等；


#### todo05
1. 单词学习主操作界面， src\views\dcyx\work-main\word-training.vue，
2. 针对单元三个模块，对于已经完成的模块，展示出该模块的分数，以中等样式；


#### todo06
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
3. 智能默写文件 src\views\dcyx\learn-main\inteli-writing.vue
4. 新增需求：
  1. 处理记忆、听写、默写完成函数 const handleWritingComplete
  传入的学习时长应该是从进入该模块后开始计算，到完成该模块后结束；而不是有效时间；

#### todo07
背景：
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
3. 智能默写文件 src\views\dcyx\learn-main\inteli-writing.vue
4. 新增需求：
  1. 智能记忆
    1. 智能记忆文件中，单词认知阶段，记录选择不认识的单词；在进入听写阶段之前，再重新过一边不认识的单词；
    2. 智能记忆文件中，单词听写阶段，记录听写失败的单词；在结束退出之前，再重新过一边听写失败的的单词；
  2. 智能听写
    1. 智能听写文件中，单词听写阶段，记录听写失败的单词；在结束退出之前，再重新过一边听写失败的的单词；
  3. 智能默写
    1. 智能默写文件中，单词默写阶段，记录默写错误的单词；在结束退出之前，再重新过一边默写错误的的单词；

需求：
1. 智能记忆文件目前的实现存在问题：
  1. 认知阶段，复习不认识的单词之后，复习完成所有不认识的单词后，要进入听写阶段。它没有进入；

#### todo08
背景：
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
3. 智能默写文件 src\views\dcyx\learn-main\inteli-writing.vue
需求：
1. 智能记忆文件
  1. 单词认识的复习阶段，不修改熟词数和生词数； unknownWords, knownWords
  2. 单词认识复习阶段完成后，进入单词听写；
  3. 单词听写复习阶段完成后，结束智能记忆模块的学习；
2. 智能听写文件
  1. 单词听写复习阶段完成后，结束智能听写模块的学习；
3. 智能默写文件
  1. 单词默写复习阶段完成后，结束智能默写模块的学习；


#### todo08
背景：
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
尽量保留原有实现的功能，包括但不限于：
1. 有效时间、暂停、结束等；
2. 快捷键绑定；
3. 更新单词状态、单元状态、单元统计的API调用；
4. 下面需求中没有提到的逻辑，尽量保留；

修改实现，符合下面的需求：
1. 智能记忆文件：
  执行流程如下：
    1. 认知阶段：
      1. [注意此处有修改：]依次显示单词列表的单词，
        1. 播放单词音频，显示当前要认知的单词拼写，单词音标、倒计时、认识、不认识按钮；
        2. 显示进度信息，显示认知完成单词数（等于认识单词列表的长度 +  不认识的单词列表长度），显示需要认知的单词总个数（等于单词列表的长度）；
        3. 显示图形化的长度进度条：
          1. 认知完成单词数 / 需要认知的单词总个数；
      2. 用户可以点击“不认识”按钮，
        1. 将单词记录到不认识的单词列表中；
        2. 将单词记录到单词认知复习单词列表中；
      3. 用户可以点击“认识”按钮，
        1. 显示中文含义
        2. 显示“确认认识” 和 “确认不认识” 按钮
        3. 点击“确认认识”按钮，将单词记录到单词认知认识的单词列表中；
        4. 点击“确认不认识”按钮，
          1. 将单词记录到单词认知不认识的单词列表中；
          2. 将单词记录到单词认知需要复习的单词列表中；
      4. 倒计时结束未选择按钮，默认将单词记录到单词认知不认识的单词列表中和单词认知需要复习的单词列表中；
      5. 点击不认识按钮，确认不认识按钮和倒计时结束未选择时，会将该单词作为不认识的单词，然后对该单词进行强化学习；
        1. 重复学习该单词两遍，每次重复加强学习时，显示单词中文含义，音标，播放单词读音，需要用户点击确认按钮；
        2. 完成强化学习后，切换下一个单词，进行单词认识；
      6. 点击认识，
        1. 显示单词中文含义
        2. 将计数器-认识的单词数+1
        3. 发送更新单词状态API；
      7. 点击确认不认识按钮和倒计时结束未选择时，
        1. 将计数器-不认识的单词数+1；
        2. 发送更新单词状态API；
      8. 认知阶段，底部信息栏显示认知阶段状态信息，显示计数器-认识的单词数， 显示计数器-不认识的单词书，显示单词总个数；
      8. 退出认知阶段，进入认识复习阶段的条件是，将单词列表中的单词全部过一遍认识操作，用户对单词列表的单词，完成一轮单词认识；
    2. 进入认知复习阶段：
      1. [注意此处有修改：]依次显示需要复习的单词列表的单词；
        1. 播放单词音频，显示认知复习的当前单词拼写，单词音标、倒计时、认识、不认识按钮；
        2. 显示进度信息，显示认知复习完成单词数（等于认知复习完成单词列表的长度），显示需要认知复习的单词总个数（等于认知需要复习的单词列表的长度）；
        3. 显示图形化的长度进度条：
          1. 认知复习完成单词数 / 需要认知复习的单词总个数；
      2. 用户可以点击“认识”按钮，
        1. 显示中文含义，
        2. 显示确认认识和确认不认识按钮；
        3. 选择确认认识按钮，将单词添加到认知复习完成的单词列表中；
      3. 用户点击不认识，确认不认识，或者倒计时结束未选择，会进入强化学习该单词阶段；
        1. 重复学习该单词两遍，每次重复加强学习时，显示单词中文含义，音标，播放单词读音，需要用户点击确认按钮；
        2. 完成强化学习后，切换下一个认知复习的单词，进行认知单词复习；
        3. 下一个需要复习的单词是，单词认知复习单词列表有的单词，同时认知复习完成单词列表中没有的单词；
      4. 点击认识按钮，将计数器-认知复习完成单词数+1；不发送API；
      5. 认知复习阶段，底部信息栏显示认知复习阶段状态信息，显示计数器-认知复习完成单词数，显示需要认知复习的单词总个数；
      6. 认知复习阶段完成后，进入听写阶段；
        1. [注意此处有修改：]认知复习阶段完成的条件，没有下一个需要复习的认知单词，即是认知复习完成单词列表的长度等于认知需要复习的单词列表；
    3. 听写阶段：
      1. [注意此处有修改：]依次显示听写单词列表的单词；
        1. 播放单词音频，显示听写阶段的当前单词的中文含义、倒计时、单词输入框；
        2. 显示进度信息，显示听写完成单词数（等于听写正确列表长度+听写错误列表长度），显示需要听写的单词总个数（等于听写单词列表的长度）；
        3. 显示图形化的长度进度条：
          1. 听写完成单词数 / 需要听写的单词总个数；
      2. 聚焦输入框，用户输入单词，键入 enter 键，用户输入完毕，检查用户输入单词；
        1. 用户拼写正确
          1. 是检查首次拼写正确，
            1. 将单词记录到听写正确的单词列表中；
            2. 更新计数器-听写正确单词数；
            3. 发送更新单词状态API；
          2. 非首次拼写正确，
            1. 切换下一个听写单词；
        2. 拼写错误，
          1. 首次拼写错误，
            1. 将单词记录到听写错误的单词列表中；
            2. 更新计数器-听写错误单词数；
            3. 将单词记录到需要复习的听写的单词列表中；
            4. 发送更新单词状态API；
            5. 显示单词正确拼写，以及用户输入单词与正确拼写的对比；
            6. 等待用户再次输入拼写
          2. 非首次拼写错误，
            1. 显示单词正确拼写，以及用户输入单词与正确拼写的对比；
            2. 等待用户再次输入拼写
      4. 底部信息栏显示听写阶段状态信息，显示计数器-听写正确单词数，显示计数器-听写错误单词数，显示需要听写的单词总个数；
      5. 进入听写复习阶段的条件是单词列表中的单词，全部过听写操作，用户对单词列表的单词，完成一轮单词听写；
    4. 听写复习阶段：
      1. [注意此处有修改：]依次显示需要复习的听写的单词列表的单词；
        1. 播放单词音频，显示听写复习的当前单词、倒计时、单词输入框；
        2. 显示进度信息，显示听写复习完成单词数（等于听写复习正确列表长度），显示需要听写复习的单词总个数（等于听写复习单词列表长度）；
        3. 显示图形化的条状长度进度条：
          1. 听写复习完成单词数 / 需要听写复习的单词总个数；
      2. 聚焦输入框，用户输入单词，键入 enter 键，用户输入完毕，检查用户输入单词；
        1. 用户拼写正确
          1. 检查首次拼写正确，
            1. 将单词记录到听写复习正确的单词列表中；
            2. 更新计数器-听写复习正确单词数；
          2. 非首次拼写正确，
            1. 切换下一个听写单词；
        2. 拼写错误，
            1. 显示单词正确拼写，以及用户输入单词与正确拼写的对比；
            2. 等待用户再次输入拼写
      3. [此处有修改]底部信息栏显示听写复习阶段状态信息，显示计数器-听写复习正确单词数，显示需要听写复习的单词总个数；
      4. 完成听写复习阶段，进入结束阶段；
        1. 听写复习阶段完成的条件是没有下一个需要复习的听写单词；
    5. 结束：
      1. 智能记忆模块结束；
注意：
1. 将每一个阶段的阶段状态信息，展示在底部的信息栏合适的分布位置；


bug：
1. 认知复习；
  1. 计数器错误；
2. 听写复习；
  1. 计数器错误
3. 进度条都没显示；
4. 底部信息栏没有状态信息；
5. 卡片中间不要有状态信息；

#### todo09
背景：
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 函数 nextRecognitionReviewWord ，找到下一个需要复习的单词；
// 认知复习阶段：下一个单词
const nextRecognitionReviewWord = () => {
  // 找到下一个需要复习的单词（在复习列表中但不在完成列表中）
  const nextIndex = recognitionReviewWords.value.findIndex(
    (word, index) =>
      index > recognitionReviewIndex.value &&
      !recognitionReviewCompletedWordIds.value.has(Number(word.id)),
  )

  if (nextIndex !== -1) {
    recognitionReviewIndex.value = nextIndex
    currentState.value = 'recognition'
    countdown.value = 5
    startCountdown()
    playWordSound()
  } else {
    // 认知复习完成，进入听写阶段
    enterDictationPhase()
  }
}
3. 需要认知复习的单词 id 集合 recognitionReviewWordIds、
4. 需要认知复习的单词列表 recognitionReviewWords、
5. 完成认知复习的单词 id 集合，recognitionReviewCompletedWordIds

需求：
1. 修改 nextRecognitionReviewWord 逻辑：
  1. 判断如果 完成认知复习的单词 id 集合 个数等于 需要认知复习的单词 id 集合 个数，则进入听写阶段；
  2. 否则，找到下一个需要认知复习的单词，进入认知复习阶段；
    1. 筛选出在需要认知复习的单词 id 集合的，但是不在完成认知复习的单词 id 集合的单词 id，组成一个集合，随机选出一个单词，从需要认知复习的单词列表中选出该单词，作为下一个需要认知复习的单词；


#### todo09
背景：
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 函数 nextDictationWord ，找到下一个听写的单词；
const nextDictationWord = () => {
  if (currentPhase.value === 'dictation') {
    if (dictationIndex.value < totalWords.value - 1) {
      dictationIndex.value++
      dictationInput.value = ''
      dictationError.value = false
      dictationErrorMap.value = []
      dictationShowSpelling.value = false
      dictationReadyForNext.value = false
      playDictationSound()
      focusDictationInput()
    } else {
      // 听写阶段完成，检查是否需要听写复习
      if (dictationFailedWordIds.value.size > 0) {
        enterDictationReviewPhase()
      } else {
        finishStudy()
      }
    }
  } else if (currentPhase.value === 'dictationReview') {
    if (dictationReviewIndex.value < dictationReviewWords.value.length - 1) {
      dictationReviewIndex.value++
      dictationInput.value = ''
      dictationError.value = false
      dictationErrorMap.value = []
      dictationShowSpelling.value = false
      dictationReadyForNext.value = false
      playDictationSound()
      focusDictationInput()
    } else {
      // 听写复习完成
      finishStudy()
    }
  }
}
需求：
1. 智能记忆文件
  1. 听写复习阶段：
      1. 依次显示需要复习的听写的单词列表的单词；
        1. 播放单词音频，显示听写复习的当前单词、倒计时、单词输入框；
        2. 显示进度信息，显示听写复习完成单词数（等于听写复习正确的单词集合长度），显示需要听写复习的单词总个数（等于听写复习单词列表长度）；
        3. 显示图形化的条状长度进度条：
          1. 听写复习完成单词数 / 需要听写复习的单词总个数；
      2. 聚焦输入框，用户输入单词，键入 enter 键，用户输入完毕，检查用户输入单词；
        1. 用户拼写正确
          1. 检查首次拼写正确，
            1. 将单词记录到听写复习正确的单词集合中；
            2. 更新计数器-听写复习正确单词数；
          2. 非首次拼写正确，
            1. 切换下一个听写单词；
            2. 下一个单词选择逻辑：
              1. 筛选出在需要听写复习的单词 id 集合的，但是不在完成听写复习的单词 id 集合的单词 id，组成一个集合，随机选出一个单词，从需要听写复习的单词列表中选出该单词，作为下一个需要听写复习的单词；
        2. 拼写错误，
            1. 显示单词正确拼写，以及用户输入单词与正确拼写的对比；
            2. 等待用户再次输入拼写
      3. [此处有修改]底部信息栏显示听写复习阶段状态信息，显示计数器-听写复习正确单词数，显示需要听写复习的单词总个数；
      4. 完成听写复习阶段，进入结束阶段；
        1. 听写复习阶段完成的条件是没有下一个需要复习的听写单词；
2. 修改 nextDictationWord 逻辑， 在判断条件之后  if (currentPhase.value === 'dictationReview') ：
  1. 判断如果 完成听写复习的单词 id 集合 个数等于 需要听写复习的单词 id 集合 个数，则进入结束阶段；
  2. 否则，找到下一个需要听写复习的单词，去听写复习；


#### todo10
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
3. 智能默写文件 src\views\dcyx\learn-main\inteli-writing.vue
需求：
1. 参考智能听写文件，实现智能记忆文件的有效时间功能和30秒无操作会暂停的功能；


#### todo11
1. 智能记忆文件 src\views\dcyx\learn-main\inteli-memory.vue
2. 智能听写文件 src\views\dcyx\learn-main\inteli-dictation.vue
参考智能记忆的听写、听写复习的实现，更智能听写文件，
尽量保留智能听写原有的功能；
1. 快捷键；
2. 样式参考智能记忆实现；
重点在下面需求：
1. 智能听写文件，
    1.听写阶段：
      1. 依次显示听写单词列表的单词；
        1. 播放单词音频，倒计时、单词输入框；
        2. 显示进度信息，显示听写完成单词数（等于听写正确列表长度+听写错误列表长度），显示需要听写的单词总个数（等于听写单词列表的长度）；
        3. 显示图形化的长度进度条：
          1. 听写完成单词数 / 需要听写的单词总个数；
      2. 聚焦输入框，用户输入单词，键入 enter 键，用户输入完毕，检查用户输入单词；
        1. 用户拼写正确
          1. 是检查首次拼写正确，
            1. 将单词记录到听写正确的单词列表中；
            2. 更新计数器-听写正确单词数；
            3. 发送更新单词状态API；
          2. 非首次拼写正确，
            1. 切换下一个听写单词；
        2. 拼写错误，
          1. 首次拼写错误，
            1. 将单词记录到听写错误的单词列表中；
            2. 更新计数器-听写错误单词数；
            3. 将单词记录到需要复习的听写的单词列表中；
            4. 发送更新单词状态API；
            5. 显示单词正确拼写，以及用户输入单词与正确拼写的对比；
            6. 等待用户再次输入拼写
          2. 非首次拼写错误，
            1. 显示单词正确拼写，以及用户输入单词与正确拼写的对比；
            2. 等待用户再次输入拼写
      4. 在底部信息栏，
        1. 显示计数器-听写正确单词数，
        2. 显示计数器-听写错误单词数，
        3. 显示需要听写的单词总个数；
        4. 显示听写阶段状态信息，
      5. 进入听写复习阶段的条件是单词列表中的单词，全部过听写操作，用户对单词列表的单词，完成一轮单词听写；
    2. 听写复习阶段：
      1. 依次显示需要复习的听写的单词列表的单词；
        1. 播放单词音频，倒计时、单词输入框；
        2. 显示进度信息，显示听写复习完成单词数（等于听写复习正确的单词集合长度），显示需要听写复习的单词总个数（等于听写复习单词列表长度）；
        3. 显示图形化的条状长度进度条：
          1. 听写复习完成单词数 / 需要听写复习的单词总个数；
      2. 聚焦输入框，用户输入单词，键入 enter 键，用户输入完毕，检查用户输入单词；
        1. 用户拼写正确
          1. 检查一次拼写正确，
            1. 将单词记录到听写复习正确的单词集合中；
            2. 更新计数器-听写复习正确单词数；
          2. 非一次拼写正确，
            1. 切换下一个听写单词；
            2. 下一个单词选择逻辑：
              1. 筛选出在需要听写复习的单词 id 集合的，但是不在完成听写复习的单词 id 集合的单词 id，组成一个集合，随机选出一个单词，从需要听写复习的单词列表中选出该单词，作为下一个需要听写复习的单词；
        2. 拼写错误，
            1. 显示单词正确拼写，以及用户输入单词与正确拼写的对比；
            2. 等待用户再次输入拼写
      3. 在底部信息栏
        1. 显示计数器-听写复习正确单词数，
        2. 显示需要听写复习的单词总个数；
        3. 显示听写复习阶段状态信息，
      4. 完成听写复习阶段，进入结束阶段；
        1. 听写复习阶段完成的条件
          1. 没有下一个需要复习的听写单词；
          2. 筛选出在需要听写复习的单词 id 集合的，但是不在完成听写复习的单词 id 集合的单词 id，组成一个集合，随机选出一个单词，从需要听写复习的单词列表中选出该单词，作为下一个需要听写复习的单词；
    3. 结束：
      1. 智能听写模块结束；

#### todo12
1. 智能听写文件，src\views\dcyx\learn-main\inteli-dictation.vue
2. 智能默写文件 src\views\dcyx\learn-main\inteli-writing.vue
需求：
参考智能听写文件，在当前智能默写实现的基础上，更新智能默写文件；
尽量保留智能默写原有的功能；新增和优化功能；
1. 智能默写文件：
  1. 默写学习阶段：
    保留原功能，更改为下面功能：
    1. 首次默写错误的单词，记录为默写错误单词，加入到需要复习的默写复习的单词列表中，默写错误单词列表；
    2. 首次默写正确的单词，记录为默写正确单词，加入到默写正确单词列表中；
  2. 默写复习阶段：
      1. 依次显示需要复习的默写复习的单词列表的单词；
        1. 展示单词中文词义，倒计时、单词输入框；
        2. 显示进度信息，显示默写复习完成单词数（等于默写复习正确的单词集合长度），显示需要默写复习的单词总个数（等于默写复习单词列表长度）；
        3. 显示图形化的条状长度进度条：
          1. 默写复习完成单词数 / 需要默写复习的单词总个数；
      2. 聚焦输入框，用户输入单词，键入 enter 键，用户输入完毕，检查用户输入单词；
        1. 用户拼写正确
          1. 检查一次拼写正确，
            1. 将单词记录到默写复习正确的单词集合中；
            2. 更新计数器-默写复习正确单词数；
          2. 非一次拼写正确，
            1. 切换下一个默写单词；
            2. 下一个单词选择逻辑：
              1. 筛选出在需要默写复习的单词 id 集合的，但是不在完成默写复习的单词 id 集合的单词 id，组成一个集合，随机选出一个单词，从需要默写复习的单词列表中选出该单词，作为下一个需要默写复习的单词；
        2. 拼写错误，
            1. 显示单词正确拼写，以及用户输入单词与正确拼写的对比；
            2. 等待用户再次输入拼写
      3. 在底部信息栏
        1. 显示计数器-默写复习正确单词数，
        2. 显示需要默写复习的单词总个数；
        3. 显示默写复习阶段状态信息，
      4. 完成默写复习阶段，进入结束阶段；
        1. 默写复习阶段完成的条件
          1. 没有下一个需要复习的默写单词；
          2. 筛选出在需要默写复习的单词 id 集合的，但是不在完成默写复习的单词 id 集合的单词 id，组成一个集合，随机选出一个单词，从需要默写复习的单词列表中选出该单词，作为下一个需要默写复习的单词；
  3. 结束：
    1. 智能默写模块结束；



#### todo13
1. 个人简介页面，src\views\dcyx\work-main\my-profile.vue
2. 个人信息对象的类型： src\api\dcyx\user.api.ts
DcyxUserInfo {
  id: number
  organizationId: number
  username: string
  fullname: string
  gender: string
  mobile: string
  schoolName: string
  grade: string
  wechatId: string
  qq: string
  lastScore: string
  expireTime: string
  loginTime: string
  email: string
  grades: string
  extend: string
  studentType: string
  firstLoginTime: string
  teacherName: string
  teacherMobile: string
  className: string
}
需求：
1. 编写个人简介页面，展示以上的信息；


#### todo14
1. 记忆追踪文件，src\views\dcyx\work-main\memory-tracking.vue
2. 记忆追踪需求描述如下：
“记忆追踪” 模块是一个用于英语学习的功能模块，主要帮助学习者进行英语知识的记忆和巩固。以下是对该模块的功能、组件元素及其特征和分布位置的整理：
功能
该模块主要围绕某一个用户选的课程展开，提供了智能记忆、智能听写、智能默写等学习功能，帮助学习者通过不同的方式来记忆和掌握英语知识，同时还可以选择不同的单元进行学习，满足个性化的学习需求。
组件元素
顶部信息栏：位于页面顶部，显示当前课程信息；
学习模块区域：位于页面左侧，包含 “智能记忆”“智能听写”“智能默写” 三个选项。其中 “智能记忆” 选项处于选中状态，背景为浅绿色，文字为深绿色；“智能听写” 和 “智能默写” 选项背景为白色，文字为深灰色。
单元选择区域：位于页面上方偏左位置，有一个下拉菜单，显示 “选择单元”，点击后可展开单元列表，列表中包含多个单元选项，如 “Unit 1 (1)”“Unit 1 (2)” 等，单元选项背景为白色，文字为深灰色。
课程信息区域：位于单元选择区域右侧，显示 “本课程共有 3 个生词，前 0 个需要立即复习。”，文字为深灰色。
功能按钮区域：位于课程信息区域右侧，包含 “智能复习”“任务复习”“下载” 三个按钮。“智能复习” 按钮为蓝色，文字为白色；“任务复习” 按钮为橙色，文字为白色；“下载” 按钮为白色，文字为深灰色。
学习内容显示区域：位于页面中间偏右位置，显示当前的学习内容 “textbook conversation aloud”，文字为深灰色。
3. 记忆追踪页面数据来源：
  1. 当前课程信息，从 dcyx_program.store 的 currentProgram.programName，位于src\store\modules\dcyx_program.store.ts
  2. 单元列表，从 dcyx_unit.store 的 unitsList，位于src\store\modules\dcyx_unit.store.ts，单元列表多一个全选选项；
  3. 当前学习内容是一个生词列表，对应某个单元的选择的学习模式（智能记忆、智能听写、智能默写）中学习过程中错误的单词；
    1. 已经实现一个API，
      /api/client/v1/user-study-record/review-words?programId=1&source=znmx&unitId=1
      programId 是已选的教材id；
      unitId 是单元 id，（如果不传 unitId， 则表示查询全部单元的生词，对应选择全选选项）；
      source 是学习模式，znjy 表示智能记忆，zntx 表示智能听写，znmx 表示智能默写；
    返回：
    {
      "code": "00000",
      "data": [
        {
          "id": "3",
          "spelling": "Please",
          "syllable": "please",
          "meaningZhCn": "请",
          "exampleEnUs": "Please come in.",
          "exampleZhCn": "请进。",
          "unitName": "Unit 1: Greetings",
          "programId": "1",
          "seriesId": "1",
          "unitId": "1",
          "status": 1
        },
        {
          "id": "8",
          "spelling": "Morning",
          "syllable": "morn-ing",
          "meaningZhCn": "早晨",
          "exampleEnUs": "Good morning!",
          "exampleZhCn": "早上好！",
          "unitName": "Unit 1: Greetings",
          "programId": "1",
          "seriesId": "1",
          "unitId": "1",
          "status": 1
        },
        {
          "id": "9",
          "spelling": "Night",
          "syllable": "night",
          "meaningZhCn": "夜晚",
          "exampleEnUs": "Good night and sweet dreams.",
          "exampleZhCn": "晚安，做个好梦。",
          "unitName": "Unit 1: Greetings",
          "programId": "1",
          "seriesId": "1",
          "unitId": "1",
          "status": 1
        }
      ],
      "msg": "一切ok"
    }
4. 实现记忆追踪功能的 VIEW ，STORE、API、composables 各个模块；
  1. VIEW 位于 src\views\dcyx\work-main\memory-tracking.vue
  2. STORE 位于 src\store\modules\dcyx_memory_tracking.store.ts
  3. API 位于 src\api\dcyx\dcyx_memory_tracking.api.ts
  4. composables 位于 src\composables\dcyx\memory_tracking.composables.ts
