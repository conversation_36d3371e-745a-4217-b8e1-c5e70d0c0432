import request from '@/utils/request'
import { UnitCountWordItem } from './units.api'
import type { UnitItem } from './units.api'

const PROGRAM_BASE_URL = 'api/client/v1/program'
const USER_CURRENT_PROGRAM_URL = 'api/client/v1/user-current-program'
const USER_CURRENT_UNIT_URL = 'api/client/v1/user-current-unit'

const DCYX_PROGRAM_API = {
  // getProgramBySeriesId
  getProgramBySeriesId: (params: ProgramQueryParams) => {
    // console.log('getProgramBySeriesId', params)
    return request<ProgramQueryParams, PageResult<ProgramItem[]>>({
      url: `${PROGRAM_BASE_URL}/page`,
      method: 'get',
      params,
    })
  },
  // getUnitsInfoByProgram
  getUnitsInfoByProgram: (
    parentId: string,
    seriesId: string,
    programId: string,
  ) => {
    return request<any, UnitItem[]>({
      url: `${PROGRAM_BASE_URL}/unit/getUnitsInfoByProgram/${parentId}/${seriesId}/${programId}`,
      method: 'get',
    })
  },
  // countWordsByUnit
  countWordsByUnit: (seriesId: string, programId: string, unitName: string) => {
    return request<any, UnitCountWordItem>({
      url: `${PROGRAM_BASE_URL}/word/countWordsByUnit/${seriesId}/${programId}/${unitName}`,
      method: 'get',
    })
  },
  // 查询用户选中当前教材项的接口
  // getCurrentUserProgram: () => {
  //   return request<void, UserCurrentProgram>({
  //     url: `${USER_CURRENT_PROGRAM_URL}/current`,
  //     method: 'get',
  //   })
  // },

  // 更新用户选中当前教材项的接口、统一使用 dcyx_user_current_unit 模块的接口
  updateCurrentUserProgram: (data: UpdateUserCurrentProgramPayload) => {
    return request<UpdateUserCurrentProgramPayload, any>({
      url: `${USER_CURRENT_UNIT_URL}/current`,
      method: 'post',
      data,
    })
  },
  // 获取当前教材项的详情
  getCurrentProgramDetail: () => {
    return request<void, CurrentProgram>({
      url: `${USER_CURRENT_PROGRAM_URL}/current-detail`,
      method: 'get',
    })
  },
}

export default DCYX_PROGRAM_API

// 定义分页查询参数接口
export interface ProgramQueryParams {
  seriesId: number
  pageNum: number
  pageSize: number
}

export interface CurrentProgram {
  seriesId: number
  seriesName: string
  semesterId: number
  semesterName: string
  programId: number
  programName: string
  programWordNumber: number
  programStudyWordNumber: number
  studyId: number
  unitName: string
}

export interface ProgramItem {
  id: number
  seriesId: number
  name: string
  totalWords: string
  expLanguage: string
  nameEnUs: string
  nameZhCn: string
  nameZhBig: string
  disporder: string
  remark: string
  type: string
  createUser: string
  createTime: string
  updateTime: string
  updateUser: string
  status: string
  studyNumber: string
  percent: number
  percentStr: string
}

export interface UserCurrentProgram {
  id: number
  userId: number
  username: string
  seriesId: number
  programId: number
  programName: string
}

export interface UpdateUserCurrentProgramPayload {
  // id: number
  // userId: number
  // username: string
  // seriesId: number
  programId: number
  unitId: number | null
}
