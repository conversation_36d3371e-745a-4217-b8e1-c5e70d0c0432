#### todo01

1、 新增 UNIT 单元模块 API 、my-vue3-project\src\api\dcyx 目录下，命名为 units.api.ts；
2、 实现两个 API
1、[get] 查询用户选中当前单元的接口 /api/client/v1/user-current-unit/current
没有参数
返回示例如下，请设置好类型，命名为 UserCurrentUnit 结构
{
"id": "6",
"userId": "6",
"username": null,
"seriesId": null,
"programId": null,
"wordId": null,
"wordIdx": 7,
"groupId": null,
"fortify": null,
"unitId": "5",
"unitName": null,
"source": null,
"errors": 0,
"rights": 0,
"isEnd": 0,
"errorWordInfo": null
},
2、[post] 更新用户选中当前单元的接口 /api/client/v1/user-current-unit/current
有参数，示例如下，请制定相应的结构：
{
"id": 0,
"userId": 6,
"username": "",
"seriesId": 0,
"programId": 0,
"wordId": 0,
"wordIdx": 0,
"groupId": "",
"fortify": "",
"unitId": 6,
"unitName": "",
"source": "",
"errors": 0,
"rights": 0,
"isEnd": 0,
"errorWordInfo": ""
}，
返回值为 NULL；
3、

#### TODO02

2. 增加 unit 功能模块对应的 项目 store 模块，位于 my-vue3-project\src\store 目录，命名为 dcyx_unit.store.ts
   1、 unit 的store 模块会调用 UNIT 单元模块的两个 API
   [get] 查询用户选中当前单元的接口 /api/client/v1/user-current-unit/current
   [post] 更新用户选中当前单元的接口 /api/client/v1/user-current-unit/current
   2、实现尽可能完善，对应 UNIT 模块的 store 模块；
   3、如果查询到 当前单元 为 NULL，则 store 维护的当前选中单元的变量就为空；
   2、修改项目 VIEW 视图模块，my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSysRightBoard\index.vue
   1、视图会调用 store 模块，
   2、视图会显示当前选中的 UNIT 模块；
   3、如果 store 维护的当前选中单元的变量为空，则显示问题，提示用户去选择学习的单元；
   4、实现尽可能完善的，对应的 UNIT 模块的 VIEW 视图；
   3、尽可能保证原有的功能不变；

#### todo03

1. 在 program 教材项模块文件 、my-vue3-project\src\api\dcyx 目录下，命名为 program.api.ts，新增下面两个API；
   1. [get] 查询用户选中当前教材项的接口 /api/client/v1/user-current-program/current
      1. 查询的返回示例：
         ```json
         {
           "id": 0,
           "userId": 0,
           "username": "",
           "seriesId": 0,
           "programId": 0,
           "programName": ""
         }
         ```
   2. [post] 更新用户选中当前教材项的接口 /api/client/v1/user-current-program/current
      1. 更新的请求参数示例：
         ```json
         {
           "id": 0,
           "userId": 0,
           "username": "",
           "seriesId": 0,
           "programId": 0
         }
         ```
      2. 返回示例：`{}`
   3. 尽可能定义完整的类型的接口；
   4. 尽可能编写完整的模块；
   5. 尽可能保留原有的代码；
2. 修改 paogram 功能模块对应的 项目 store 模块，位于 my-vue3-project\src\store 目录，命名为 dcyx_program.store.ts，新增下面功能
   1. program 的store 模块会调用 program 教材项模块的两个 API
      [get] 查询用户选中当前教材项的接口 /api/client/v1/user-current-program/current
      [post] 更新用户选中当前教材项的接口 /api/client/v1/user-current-program/current
3. 如果查询到 当前教材项 为 NULL，则 store 维护的当前选中教材项的变量就为空；
4. 修改项目 VIEW 视图模块，my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSysRightBoard\index.vue
   1. 显示当前教材项的信息；视图会显示当前选中的 program 模块
   2. 尽可能显示所有的字段，调整合适的样式和间隔；；
5. 如果 store 维护的当前选中教材项的变量为空，则显示提示信息，提示用户去选择学习的教材项；；
6. 尽可能完善地实现对应 program 模块的 API、store、VIEW 模块；
7. 尽可能保证原有的功能不变

#### todo04

1. 现在有个了该接口 [get] /api/client/v1/program/current-detail
2. 请你在 program.api.ts 中实现一个函数请求该API；my-vue3-project\src\api\dcyx\program.api.ts
   1. 定义合理的结构，下面是一个返回示例：
      ```json
      {
      	"seriesId": "4",
      	"seriesName": "初中语法精讲",
      	"semesterId": "2",
      	"semesterName": "2023秋季学期",
      	"programId": "6",
      	"programName": "初中语法突破",
      	"programWordNumber": "400",
      	"programStudyWordNumber": "0",
      	"studyId": "16",
      	"unitName": ""
      },
      ```
      没有请求参数
3. 请你在 program.store.ts 实现相应的函数调用和一个状态变量保存， my-vue3-project\src\store\modules\dcyx_program.store.ts
4. 请你在 DcyxSysRightBoard 组件中，展示相应的信息，my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSysRightBoard\index.vue
5. 尽可能完善地实现对应的 API、store、VIEW 模块；
6. 尽可能保证原有的功能不变

#### todo05

1. 请你在 DcyxSysRightBoard 组件中，实现相应的逻辑，my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSysRightBoard\index.vue
   1. 如果有 dcyxProgramStore.currentProgramDetail 信息，点击该项，触发 onEnterProgram 函数，进入教材学习 router.push('/work/word-training')
   2. 如果没有信息，点击该项，触发 onOpenProgramSelect 函数，打开 DcyxNavBar 组件的 my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSelectStudyModule\index.vue 组件；
      1. 组件路径：my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxNavBar\index.vue
      2. 组件路径：my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSelectStudyModule\index.vue

#### todo06

1. 已有后端接口：
   1. /api/client/v1/program/page [get]
   2. 支持查询参数
      `txt
   pageNum 页码,示例值(1) query true string
   pageSize 每页记录数,示例值(10) query true string
   seriesId 所属系列ID query false string
   name 教材名称 query false string
   totalWords 总单词数 query false string
   nameZhCn 教材简体中文名 query false string
`
2. 实现 program 模块的需求：
   1. 实现 API 根据 seriesId 查询单元列表；my-vue3-project\src\api\dcyx\program.api.ts
   2. 实现 SOTER 函数，调用 API ，并返回数据； my-vue3-project\src\store\modules\dcyx_program.store.ts
   3. 实现 VIEW 视图，根据逻辑显示数据；my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSelectStudyModule\index.vue
      1. 默认加载时，显示首个 series 的所有 program
      2. 点击不同的 series 时，显示该 series 下的所有 program
3. 尽可能完善地实现对应的 API、store、VIEW 模块；
4. 尽可能保证原有的功能不变

#### todo06

// getUnitsInfoByProgram
getUnitsInfoByProgram: (
parentId: string,
seriesId: string,
programId: string,
) => {
return request<any, UnitItem[]>({
url: `${PROGRAM_BASE_URL}/unit/getUnitsInfoByProgram/${parentId}/${seriesId}/${programId}`,
method: 'get',
})
},
// countWordsByUnit
countWordsByUnit: (seriesId: string, programId: string, unitName: string) => {
return request<any, UnitCountWordItem>({
url: `${PROGRAM_BASE_URL}/word/countWordsByUnit/${seriesId}/${programId}/${unitName}`,
method: 'get',
})
},

1. 实现 unit 模块的两个需求：
   1. 根据 programid

#### todo07

1. 帮我检查和优化 program 和 unit 两个模块的 store；
   my-vue3-project\src\store\modules\dcyx_program.store.ts
   my-vue3-project\src\store\modules\dcyx_series.store.ts
2. 尽可能完善地实现对应的 API、store、VIEW 模块；
3. 尽可能保证原有的功能不变

以上需求你已经完成了；

补充额外的需求：

1. store 中其中调用了 API 的函数，希望能够以 return new Promise 的结构编写；

#### todo08

1. 因为重构了 program 和 unit 两个模块；
2. 视图也需要优化和修改，调用修改后的 store 的函数；
   1. 视图实现函数，调用了 store 提供函数的话，需要用 try catch 包裹，并 catch 语句中打印本函数名；
3. 视图文件位于 my-vue3-project\src\views\dcyx\sys-main 目录；

#### todo09

1. my-vue3-project\src\store\modules\dcyx_program.store.ts 文件中
   initProgramStore 该函数执行时，console.log('currentProgramId', currentProgramId.value)，会打印下面信息，currentProgramId undefined

控制台报错：
dcyx_program.store.ts:141 currentProgramId 不存在

2. 请解决问题；
3. fetchCurrentProgram 函数准备弃用；请勿调用；
4. initUnitStore 函数执行成功会将数据保存到 currentUnit 值；
5. currentProgramId 通过计算 currentUnit 值得到；
   const currentProgramId = computed(() => {
   return dcyxUnitStore.currentUnit?.programId
   })

#### todo10

1. 重新编写一个主页 VIEW 视图，

#### todo11

1. 如何确保 store 状态，依次初始化都能成功？，位于 my-vue3-project\src\views\dcyx\sys-main\index.vue 的 initDashboard 函数中；
2. await dcyxUnitStore.initUnitStore() store 中的变量都加载完毕后再执行下面的逻辑？

#### todo12

1. 在 program store 模块新增两个变量 currentProgramId、currentSeriesId，分别等于 unit 模块的 currentUnit 的 seriesId: number、 programId: number 属性；program store 模块 位于 my-vue3-project\src\store\modules\dcyx_program.store.ts;
2. 在 series store 模块新增一个变量 currentSemesterId 等于 等于 unit 模块的 currentUnit 的 semesterId: number；series store 模块位于 my-vue3-project\src\store\modules\dcyx_series.store.ts
3. my-vue3-project\src\store\modules\dcyx_unit.store.ts 模块位置；

#### todo13

1. 修改视图文件，需求如下，位于my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSelectStudyModule\index.vue
2. 初始时，
   my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSelectStudyModule\index.vue

#### todo14

1. 开发今日单词学习数量报告，位于 my-vue3-project\src\views\dcyx\sys-main\compoments\DcyxSysRightBoard\index.vue、函数 initTodayVocabInfo
2. 显示：
   1. 学习时长；
   2. 显示学习单词数量；
   3. 显示学习速度 数量/时长；
3. 现在已有 API 接口；
   1. /api/client/v1/user-study-word-history/today
   2. 返回示例为：
      ```json
      {
      	"studyDuration": 0,
      	"wordCount": 0,
      	"studySpeed": 0
      },
      ```
