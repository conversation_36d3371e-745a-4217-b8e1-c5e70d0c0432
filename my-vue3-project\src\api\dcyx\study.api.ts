import request from '@/utils/request'

const STUDY_BASE_URL = '/api/v1/study'

const DCYX_STUDY_API = {
  getTodayTimeRecord: () => {
    return request<any, TodayTimeRecord>({
      url: `${STUDY_BASE_URL}/time/getTodayTimeRecord`,
      method: 'get',
    })
  },
  getTodayVocabReport: () => {
    return request<any, TodayVocabReport>({
      url: '/api/client/v1/user-study-word-history/today',
      method: 'get',
    })
  },
}

export default DCYX_STUDY_API

export interface TodayTimeRecord {
  time1: string
  time2: string
  efficiency: string
  seconds: string
}

export interface TodayVocabReport {
  studyDuration: number
  wordCount: number
  studySpeed: number
}
