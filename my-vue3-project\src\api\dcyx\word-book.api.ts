import request from '@/utils/request'

const WORD_BOOK_BASE_URL = '/api/client/v1/word-book'

const DCYX_WORD_BOOK_API = {
  /**
   * 获取单词列表（按教材和单元）
   * @param params 查询参数
   */
  getWordList: (params: WordListQueryParams) => {
    return request<WordListQueryParams, PageResult<WordBookItem[]>>({
      url: `${WORD_BOOK_BASE_URL}/words`,
      method: 'get',
      params,
    })
  },

  /**
   * 获取单词音频
   * @param word 单词字符串
   */
  getWordAudio: (word: string) => {
    return request({
      url: `/api/client/v1/audio/${encodeURIComponent(word)}`,
      method: 'get',
      responseType: 'blob',
    })
  },

  /**
   * 获取教材列表
   * @param params 查询参数
   */
  getProgramList: (params: ProgramListQueryParams) => {
    return request<ProgramListQueryParams, PageResult<ProgramItem[]>>({
      url: '/api/client/v1/program/page',
      method: 'get',
      params,
    })
  },

  /**
   * 获取单元列表
   * @param params 查询参数
   */
  getUnitList: (params: UnitListQueryParams) => {
    return request<UnitListQueryParams, PageResult<UnitItem[]>>({
      url: '/api/client/v1/unit/page',
      method: 'get',
      params,
    })
  },
}

export default DCYX_WORD_BOOK_API

// 单词列表查询参数
export interface WordListQueryParams extends PageQuery {
  programId?: number
  unitId?: number
  seriesId?: number
}

// 教材列表查询参数
export interface ProgramListQueryParams extends PageQuery {
  seriesId?: number
}

// 单元列表查询参数
export interface UnitListQueryParams extends PageQuery {
  programId: number
}

// 单词本单词项
export interface WordBookItem {
  id: string
  spelling: string // 单词拼写
  syllable: string // 音标
  meaningZhCn: string // 中文词义
  exampleEnUs: string // 英文例句
  exampleZhCn: string // 中文例句
  unitName: string // 单元名称
  unitId: string // 单元ID
  programId: string // 教材ID
  seriesId: string // 系列ID
  status: number // 状态
  wordIndex?: number // 单词序号
}

// 教材项
export interface ProgramItem {
  id: number
  seriesId: number
  name: string
  nameZhCn: string
  nameEnUs: string
  totalWords: string
  expLanguage: string
  disporder: string
  remark: string
  type: string
  status: string
  studyNumber: string
  percent: number
  percentStr: string
  createTime: string
  updateTime: string
}

// 单元项
export interface UnitItem {
  id: number
  seriesId: number
  programId: number
  programName: string
  name: string
  nameEnUs: string
  nameZhCn: string
  unitIndex: number
  totalNum: number
  status: number
  createTime: string
  updateTime: string
}

// 单词显示控制状态
export interface WordDisplayState {
  showSpelling: boolean // 是否显示拼写
  showMeaning: boolean // 是否显示词义
}
