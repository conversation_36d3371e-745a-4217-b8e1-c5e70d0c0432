// import { defineStore } from 'pinia'
import type { DcyxUserInfo } from '@/api/dcyx/user.api'
import { DCYX_USER_API } from '@/api/dcyx/user.api'
import { store } from '@/store'

export const useDcyxUserStore = defineStore('dcyx_user', () => {
  const userInfo = useStorage<DcyxUserInfo>('dcyx_user_info', {
    id: '0',
    organizationId: '',
    username: '',
    fullname: '',
    gender: '',
    mobile: '',
    schoolName: '',
    grade: '',
    wechatId: '',
    qq: '',
    lastScore: '0',
    expireTime: '',
    loginTime: '',
    email: '',
    grades: '',
    extend: '',
    studentType: '',
    firstLoginTime: '',
    teacherName: '',
    teacherMobile: '',
    className: '',
  } as DcyxUserInfo)

  const getUserInfo = async () => {
    return new Promise<DcyxUserInfo>((resolve, reject) => {
      DCYX_USER_API.getUserInfo()
        .then((res) => {
          Object.assign(userInfo.value, { ...res })
          resolve(res)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  const fetchUser = async (userid: string) => {
    return new Promise((resolve, reject) => {
      DCYX_USER_API.getUserById(userid)
        .then((res) => {
          userInfo.value = res
          resolve(res)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  return {
    userInfo,
    getUserInfo,
    fetchUser,
  }
})

export function useDcyxUserStoreHook() {
  return useDcyxUserStore(store)
}
