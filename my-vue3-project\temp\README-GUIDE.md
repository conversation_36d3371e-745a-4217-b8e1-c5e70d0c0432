[完成]TODO1：
1、参考现有的 API定义，去定义适合本项目的API接口文档；
2、使用 vite-plugin-mock，编写符合规范的代码，实现 mock 模块，模拟该文档中的API；
3、下面是规范：

- **统一接口格式**：使用 TypeScript 定义数据结构，增强代码可维护性。
- **处理传入参数**：模拟多种请求场景，提升接口灵活度。
- **利用 Mock.js 生成随机数据**：做到数据真实且多样化。
- **抽象公共函数**：控制数据量，模拟异常，避免性能瓶颈。
- **按模块拆分 Mock 文件**：开发环境启用，保证代码清晰安全。
  4、以你的经验去适当优化；

TODO2:
1、基于 mock 目录下的 API，例如 login、program、series、study、user 等模块 ，设计一个基于 mysql 数据库的数据库模式；
2、以你的经验进行优化；
3、描述数据库中各个表的设计；
4、给出数据库的见表 SQL 代码；
