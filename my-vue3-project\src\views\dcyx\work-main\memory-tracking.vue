<template>
  <div class="memory-tracking">
    <!-- 顶部信息栏 -->
    <div class="memory-tracking__header">
      <div class="course-info">
        <h2 class="course-title">
          {{ currentProgram?.programName || '英语基础课程' }}
        </h2>
        <p class="course-subtitle">
          当前课程：{{ currentProgram?.unitName || '英语口语训练' }}
        </p>
      </div>
    </div>

    <div class="memory-tracking__main">
      <!-- 学习模块区域 -->
      <div class="learning-modules">
        <div
          class="module-item"
          :class="{ 'module-item--active': activeModule === 'memory' }"
          @click="activeModule = 'memory'"
        >
          <el-icon><Reading /></el-icon>
          <span>智能记忆</span>
        </div>
        <div
          class="module-item"
          :class="{ 'module-item--active': activeModule === 'dictation' }"
          @click="activeModule = 'dictation'"
        >
          <el-icon><Microphone /></el-icon>
          <span>智能听写</span>
        </div>
        <div
          class="module-item"
          :class="{ 'module-item--active': activeModule === 'writing' }"
          @click="activeModule = 'writing'"
        >
          <el-icon><EditPen /></el-icon>
          <span>智能默写</span>
        </div>
      </div>

      <div class="memory-tracking__content">
        <!-- 单元选择区域 -->
        <div class="unit-selection">
          <el-select
            v-model="selectedUnit"
            placeholder="选择单元"
            class="unit-select"
            :loading="loading"
          >
            <el-option
              v-for="unit in unitsList"
              :key="unit.value"
              :label="unit.label"
              :value="unit.value"
            />
          </el-select>
        </div>

        <!-- 课程信息区域 -->
        <div class="course-stats">
          <p class="stats-text">
            本课程共有 {{ totalWords }} 个生词，前
            {{ needReviewCount }} 个需要立即复习。
          </p>
        </div>

        <!-- 功能按钮区域 -->
        <div class="action-buttons">
          <el-button
            type="primary"
            class="action-btn action-btn--primary"
            @click="handleSmartReview"
            :loading="loading"
          >
            智能复习
          </el-button>
          <el-button
            type="warning"
            class="action-btn action-btn--warning"
            @click="handleTaskReview"
            :loading="loading"
          >
            任务复习
          </el-button>
          <el-button
            class="action-btn action-btn--download"
            @click="handleDownload"
            :loading="loading"
          >
            下载
          </el-button>
        </div>

        <!-- 学习内容显示区域 -->
        <div class="learning-content">
          <div class="content-card">
            <h3 class="content-title">当前学习内容</h3>
            <div class="content-text">
              {{ currentContent }}
            </div>
            <div class="content-actions">
              <el-button
                type="primary"
                size="small"
                @click="handleStartLearning"
                :loading="loading"
              >
                开始学习
              </el-button>
              <el-button size="small" @click="handlePreview" :loading="loading">
                预览
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { Reading, Microphone, EditPen } from '@element-plus/icons-vue'
import { useMemoryTracking } from '@/composables/dcyx/memory_tracking.composables'

// 使用composables
const {
  activeModule,
  selectedUnit,
  currentProgram,
  unitsList,
  currentContent,
  totalWords,
  needReviewCount,
  loading,
  handleSmartReview,
  handleTaskReview,
  handleDownload,
  handleStartLearning,
  handlePreview,
  initialize,
} = useMemoryTracking()

// 组件挂载时初始化
onMounted(() => {
  initialize()
})
</script>

<style lang="scss" scoped>
.memory-tracking {
  padding: 20px;
  height: 100%;
  background-color: var(--el-bg-color-page);

  &__header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  &__main {
    display: flex;
    gap: 24px;
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.course-info {
  .course-title {
    margin: 0 0 8px;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .course-subtitle {
    margin: 0;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.learning-modules {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}

.module-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--el-border-color-light);

  &:hover {
    border-color: var(--el-color-primary);
  }

  &--active {
    background-color: #f0f9ff;
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);

    .el-icon {
      color: var(--el-color-primary);
    }
  }

  .el-icon {
    font-size: 18px;
    color: var(--el-text-color-regular);
  }

  span {
    font-size: 14px;
    font-weight: 500;
  }
}

.unit-selection {
  .unit-select {
    width: 200px;
  }
}

.course-stats {
  .stats-text {
    margin: 0;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  &--primary {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
    color: #fff;
  }

  &--warning {
    background-color: var(--el-color-warning);
    border-color: var(--el-color-warning);
    color: #fff;
  }

  &--download {
    background-color: #fff;
    border-color: var(--el-border-color);
    color: var(--el-text-color-regular);
  }
}

.learning-content {
  flex: 1;
}

.content-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-title {
  margin: 0 0 16px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.content-text {
  flex: 1;
  padding: 16px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  word-break: break-word;
  text-align: center;
}

.content-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style>
