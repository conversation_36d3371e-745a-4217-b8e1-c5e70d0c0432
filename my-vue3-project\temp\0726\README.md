#### todo01
背景：
1. 介绍单词本功能；
2. 支持选择教材、选择单元、以分页表格行显示每一个单词项；四列分别是序号、音标/读音、单词拼写(带按钮，是否显示)、词义(带按钮，是否显示)；
3. 单词本功能，支持点击单词拼写的单元cell，显示单词拼写信息；点击单词词义cell ，显示单词词义信息；点击音标，播放单词读音；
需求：
1. 完成单词本功能
项目文件：
1. 测试中心视图文件，src/views/dcyx/work-main/word-book.vue
2. 测试中心 API 文件，src/api/dcyx/word-book.api.ts
3. 测试中心 STORE 文件，src/store/modules/dcyx_word_book.store.ts

代码规范：
1. 实现有完整的 API、STORE、VIEW、模块；
  1. VIEW 中，使用了 STORE、COMPOSABLE 模块函数的函数，外部使用 try-catch 风格捕获错误；
  2. VIEW 中，使用本地变量保存数据；
  3. VIEW 中，使用 COMPONENT 模块组件；
  4. STORE 中，使用了 API 模块函数的函数，内部使用 new promise 风格包裹，输入和输出指定对象的结构类型；
  5. API 中，定义完整的数据对象结构， export interafec 导出结构， API 函数输入和输出指定对象的结构类型；
2. 函数单一职责，稳定性强，可读性强，合理模块化，代码按照VUE3最佳实践风格编写；


#### todo02
背景：
1. 介绍测试中心功能，描述如下：
  1. 支持选择教材 program，一个 program 有多个单元 unit，支持查看当前 program 的单元是否完成测试；
  2. 每个单元支持点击开始测试按钮；
  3. 支持查询测试记录；
2. 页面布局：
  1. 顶部栏：选择按钮，选择教材项；支持选择：测试中心|测试记录；
  2. 测试中心中间内容区：
    1. 以分页表格组件，显示选择教材的所有单元名、单元是否完成测试及单元的测试功能区（包含开始测试按钮）；
  3. 测试记录中间内容区：
    1. 以分页表格组件，显示选择教材的所有单元测试记录，显示单元名，测试时间，测试分数，测试评语；
    2. 支持点击单元测试记录，跳转到测试详情页；显示错误的单词，错误的题目类型；

项目文件：
1. 测试中心视图文件，src/views/dcyx/work-main/test-center.vue
2. 测试中心 API 文件，src/api/dcyx/test-center.api.ts
3. 测试中心 STORE 文件，src/store/modules/dcyx_test_center.store.ts

需求：
1. 完成测试中心需求；

代码规范：
1. 实现有完整的 API、STORE、VIEW、模块；
  1. VIEW 中，使用了 STORE、COMPOSABLE 模块函数的函数，外部使用 try-catch 风格捕获错误；
  2. VIEW 中，使用本地变量保存数据；
  3. VIEW 中，使用 COMPONENT 模块组件；
  4. STORE 中，使用了 API 模块函数的函数，内部使用 new promise 风格包裹，输入和输出指定对象的结构类型；
  5. API 中，定义完整的数据对象结构， export interafec 导出结构， API 函数输入和输出指定对象的结构类型；
2. 函数单一职责，稳定性强，可读性强，合理模块化，代码按照VUE3最佳实践风格编写；
