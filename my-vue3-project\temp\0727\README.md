#### todo01
1. 文件 src\views\dcyx\work-main\test-center.vue
2. 进入该文件，挂载时，先判断 pragramStore 有没有正常获取到 currentProgram 的值；
3. 如果没有则，参考 src\views\dcyx\work-main\word-training.vue 文件 initWordTraining 的实现方式，先获取 currentProgram 的值；

#### todo02
1. 文件 src\views\dcyx\work-main\test-center.vue
2. 点击开始测试，进入测试试卷页面，实现该页面，位于  src\views\dcyx\work-main 目录；
3. 已经实现API：
  1. /api/client/v1/test/unit-test/{unitId} 获取单元测试试卷内容
  2. 返回体：
  title 对应测试试卷名称、type 对应单元测试类别，second 为试卷可用的测试时间，单位为秒；totalQuestionNum 为问题类别；
  etcTranslation 为英文翻译为中文；选择从四个答案中选择一个正确答案，correct 标记为正确答案；
  cteTranslation 为中文翻译为英文；选择从四个答案中选择一个正确答案，correct 标记为正确答案；
  hearingTranslation 为听力理解；选择从四个答案中选择一个正确答案，correct 标记为正确答案；
  {
	"code": "00000",
	"data": {
		"testId": "13",
		"title": "单元测试 - 第一单元：问候语",
		"type": 2,
		"second": "1800",
		"totalQuestionNum": "10",
		"etcTranslation": [
			{
				"wordId": "5",
				"spelling": "Sorry",
				"meaning": "抱歉",
				"answers": [
					{
						"spelling": "钢笔",
						"meaning": "钢笔",
						"correct": 0
					},
					{
						"spelling": "抱歉",
						"meaning": "抱歉",
						"correct": 1
					},
					{
						"spelling": "天气",
						"meaning": "天气",
						"correct": 0
					},
					{
						"spelling": "椅子",
						"meaning": "椅子",
						"correct": 0
					}
				]
			},
			{
				"wordId": "6",
				"spelling": "Yes",
				"meaning": "是的",
				"answers": [
					{
						"spelling": "兄弟",
						"meaning": "兄弟",
						"correct": 0
					},
					{
						"spelling": "写",
						"meaning": "写",
						"correct": 0
					},
					{
						"spelling": "是的",
						"meaning": "是的",
						"correct": 1
					},
					{
						"spelling": "袜子",
						"meaning": "袜子",
						"correct": 0
					}
				]
			},
			{
				"wordId": "7",
				"spelling": "No",
				"meaning": "不",
				"answers": [
					{
						"spelling": "不",
						"meaning": "不",
						"correct": 1
					},
					{
						"spelling": "橡皮",
						"meaning": "橡皮",
						"correct": 0
					},
					{
						"spelling": "冷的",
						"meaning": "冷的",
						"correct": 0
					},
					{
						"spelling": "老师",
						"meaning": "老师",
						"correct": 0
					}
				]
			}
		],
		"cteTranslation": [
			{
				"wordId": "3",
				"spelling": "Please",
				"meaning": "请",
				"answers": [
					{
						"spelling": "Please",
						"meaning": "请",
						"correct": 1
					},
					{
						"spelling": "Pen",
						"meaning": "钢笔",
						"correct": 0
					},
					{
						"spelling": "Mother",
						"meaning": "母亲",
						"correct": 0
					},
					{
						"spelling": "Rabbit",
						"meaning": "兔子",
						"correct": 0
					}
				]
			},
			{
				"wordId": "1",
				"spelling": "Hello",
				"meaning": "你好",
				"answers": [
					{
						"spelling": "Mother",
						"meaning": "母亲",
						"correct": 0
					},
					{
						"spelling": "Eight",
						"meaning": "八",
						"correct": 0
					},
					{
						"spelling": "Hello",
						"meaning": "你好",
						"correct": 1
					},
					{
						"spelling": "Milk",
						"meaning": "牛奶",
						"correct": 0
					}
				]
			},
			{
				"wordId": "4",
				"spelling": "Thank",
				"meaning": "感谢",
				"answers": [
					{
						"spelling": "Eight",
						"meaning": "八",
						"correct": 0
					},
					{
						"spelling": "Father",
						"meaning": "父亲",
						"correct": 0
					},
					{
						"spelling": "Five",
						"meaning": "五",
						"correct": 0
					},
					{
						"spelling": "Thank",
						"meaning": "感谢",
						"correct": 1
					}
				]
			},
			{
				"wordId": "2",
				"spelling": "Goodbye",
				"meaning": "再见",
				"answers": [
					{
						"spelling": "Write",
						"meaning": "写",
						"correct": 0
					},
					{
						"spelling": "Goodbye",
						"meaning": "再见",
						"correct": 1
					},
					{
						"spelling": "Finger",
						"meaning": "手指",
						"correct": 0
					},
					{
						"spelling": "Black",
						"meaning": "黑色",
						"correct": 0
					}
				]
			}
		],
		"hearingTranslation": [
			{
				"wordId": "9",
				"spelling": "Night",
				"meaning": "夜晚",
				"answers": [
					{
						"spelling": "Night",
						"meaning": "夜晚",
						"correct": 1
					},
					{
						"spelling": "Egg",
						"meaning": "鸡蛋",
						"correct": 0
					},
					{
						"spelling": "Monkey",
						"meaning": "猴子",
						"correct": 0
					},
					{
						"spelling": "Warm",
						"meaning": "温暖的",
						"correct": 0
					}
				]
			},
			{
				"wordId": "10",
				"spelling": "Friend",
				"meaning": "朋友",
				"answers": [
					{
						"spelling": "Mouth",
						"meaning": "嘴巴",
						"correct": 0
					},
					{
						"spelling": "Friend",
						"meaning": "朋友",
						"correct": 1
					},
					{
						"spelling": "Eraser",
						"meaning": "橡皮",
						"correct": 0
					},
					{
						"spelling": "Panda",
						"meaning": "熊猫",
						"correct": 0
					}
				]
			},
			{
				"wordId": "8",
				"spelling": "Morning",
				"meaning": "早晨",
				"answers": [
					{
						"spelling": "Chair",
						"meaning": "椅子",
						"correct": 0
					},
					{
						"spelling": "Egg",
						"meaning": "鸡蛋",
						"correct": 0
					},
					{
						"spelling": "Morning",
						"meaning": "早晨",
						"correct": 1
					},
					{
						"spelling": "Brother",
						"meaning": "兄弟",
						"correct": 0
					}
				]
			}
		]
	},
	"msg": "一切ok"
}
4. etcTranslation 显示单词拼写，答案显示中文含义；
  cteTranslation 显示中文，答案显示单词拼写；
  hearingTranslation 显示播放音频按钮，答案显示中文含义；
5. 支持时间结束后，自动验证测试试卷的每一道题是否答对,并保存一份测试结果，记录下面信息；
  1. 记录测试时间，记录测试分数，记录正确的题目数量，记录错误的题目数量；
  2. 记录错误的单词，记录错误的题目类型；
  3. 记录正确的单词，记录正确的题目类型；
  4. 记录未选择的单词，记录未选择的题目类型；、
  5. 记录测试试卷的Id;

