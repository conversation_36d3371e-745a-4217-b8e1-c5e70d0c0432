import request from '@/utils/request'

export const DCYX_MEMORY_API = {
  // 单元单词列表
  getWordsByUnit: (params: WordsQuery) =>
    request<any, { list: WordItem[]; total: number }>({
      url: '/api/client/v1/word/page',
      method: 'get',
      params,
    }),

  // 用户单词学习状态
  saveUserWordStatus: (data: UserWordStatusUpdate) =>
    request<UserWordStatus, any>({
      url: '/api/client/v1/user-word-status/update',
      method: 'post',
      data,
    }),

  updateUserWordStatus: (id: string, data: UserWordStatus) =>
    request<UserWordStatus, any>({
      url: `/api/client/v1/user-word-status/${id}`,
      method: 'put',
      data,
    }),

  // 用户学习记录
  saveUserStudyRecord: (data: UserStudyRecordUpdate) =>
    request<UserStudyRecord, any>({
      url: '/api/client/v1/user-study-record/update',
      method: 'post',
      data,
    }),

  updateUserStudyRecord: (id: string, data: UserStudyRecord) =>
    request<UserStudyRecord, any>({
      url: `/api/client/v1/user-study-record/${id}`,
      method: 'put',
      data,
    }),

  // 更新单元学习状态
  addUnitStudyStatus: (data: UnitStudyStatus) =>
    request<UnitStudyStatus, any>({
      url: '/api/client/v1/unit-study-status/update',
      method: 'post',
      data,
    }),
  getUnitStudyStatus: (params: UnitStudyStatusQuery) =>
    request<any, PageResult<UnitStudyStatus[]>>({
      url: '/api/client/v1/unit-study-status/page',
      method: 'get',
      params,
    }),

  addUnitStudyStats: (data: UnitStudyStats) =>
    request<UnitStudyStats, any>({
      url: '/api/client/v1/unit-study-stats/update',
      method: 'post',
      data,
    }),
  /**
   * 获取单词音频
   * @param word 单词字符串
   * @returns mp3 文件流
   */
  getWordAudio: (word: string) =>
    request({
      url: `/api/client/v1/audio/${encodeURIComponent(word)}`,
      method: 'get',
      responseType: 'blob',
    }),

  // 用户单词学习状态分页列表
  getUserWordStatusPage: (params: UserWordStatusQuery) =>
    request<any, PageResult<UserWordStatus[]>>({
      url: '/api/client/v1/user-word-status/page',
      method: 'get',
      params,
    }),

  // 用户学习记录分页查询
  getUserStudyRecordPage: (params: UserStudyRecordQuery) =>
    request<any, PageResult<UserStudyRecord[]>>({
      url: '/api/client/v1/user-study-record/page',
      method: 'get',
      params,
    }),
}

export interface UnitStudyStatusQuery {
  unitId: number
  userId: number
}

export interface WordsQuery {
  unitId: number
  pageNum: number
  pageSize: number
}

export interface UserStudyRecordQuery {
  pageNum: number
  pageSize: number
  wordId?: number
  userId?: number
}

/**
 * 用户单词学习状态分页查询参数
 */
export interface UserWordStatusQuery {
  pageNum: number
  pageSize: number
  wordId?: number
  userId?: number
}

// 单词类型定义
export interface Word {
  id: string
  spelling: string
  syllable: string
  meaningZhCn: string
  exampleEnUs: string
  exampleZhCn: string
  unitName: string
}

/**
 * 单词对象
 */
export interface WordItem {
  id: string // 单词ID
  spelling: string // 单词拼写
  syllable: string // 音标
  meaningZhCn: string // 中文释义
  exampleEnUs: string // 英文例句
  exampleZhCn: string // 中文例句
  unitName: string // 单元名
  programId: string // 项目ID
  seriesId: string // 系列ID
  unitId: string // 单元ID
  status: number // 状态
}

/**
 * 用户单词学习状态
 */
export interface UserWordStatus {
  id: number // 记录ID
  userId: number // 用户ID
  wordId: number // 单词ID
  unitName: string // 单元名
  programId: number // 项目ID
  seriesId: number // 系列ID
  learned: number // 是否学会 1/0
  studyCount: number // 学习次数
  correctCount: number // 正确次数
  errorCount: number // 错误次数
  lastStudyAt: string // 最后学习时间 yyyy-MM-dd HH:mm:ss
}

/**
 * 用户单词学习状态更新结构
 */
export interface UserWordStatusUpdate {
  wordId: number // 单词ID
  learned: number // 是否学会 1/0
}

/**
 * 用户学习记录查询结构
 */
export interface UserStudyRecord {
  id: number // 记录ID
  userId: number // 用户ID
  seriesId: number // 系列ID
  programId: number // 项目ID
  wordId: number // 单词ID
  unitId: number // 单元ID
  unitName: string // 单元名
  status: number // 是否学会 1/0
  source: string // 来源
  studyId: number // 学习ID
  sourceFrom: string // 来源说明
  spellType: number // 拼写类型
  isTestWord: number // 是否测试单词
  createUser: number // 创建人
  updateUser: number // 更新人
}

/**
 * 用户学习记录更新结构
 */
export interface UserStudyRecordUpdate {
  wordId: number // 单词ID
  status: number // 是否学会 1/0
  source: string // 来源
  // spellType: number // 拼写类型
  sourceFrom: string // 来源说明
}

/**
 * 单元学习状态
 */
export interface UnitStudyStatus {
  unitId: number // 单元ID
  source: number // 来源 0-智能记忆、1-听写、2-默写
  memoryCompleted: number // 智能记忆完成 1/0
  memoryScore: number // 智能记忆得分
  memoryTimeConsuming: number // 智能记忆耗时
}

/**
 * 单元学习状态更新结构
 */
export interface UnitStudyStatusUpdate {
  id: number // 记录ID
  userId: number // 用户ID
  unitId: number // 单元ID
  memoryCompleted: number // 智能记忆完成 1/0
  memoryScore: number // 智能记忆得分
  memoryTimeConsuming: number // 智能记忆耗时
  dictationCompleted: number // 听写完成 1/0
  dictationScore: number // 听写得分
  dictationTimeConsuming: number // 听写耗时
  writingCompleted: number // 写作完成 1/0
  writingScore: number // 写作得分
  writingTimeConsuming: number // 写作耗时
  overallCompleted: number // 总完成 1/0
}

/**
 * 用户单元学习统计
 * {
  "unitId": 1,
  "unitTotal": 10,
  "studyNumZnjy": 8,
  "studyNumZnmx": 0,
  "studyNumZntx": 0,
  "testTimeZnjy": 1000,
  "testTimeZnmx": 0,
  "testTimeZntx": 0,
  "restudyNumZnjy": 2,
  "restudyNumZnmx": 0,
  "restudyNumZntx": 0,
  "isEndZnjy": 1,
  "isEndZnmx": 0,
  "isEndZntx": 0
}
 */
export interface UnitStudyStats {
  unitId: number // 单元ID
  unitTotal: number // 单元总单词数
  studyNumZnjy: number // 智能记忆学习次数
  testTimeZnjy: number // 智能记忆学习耗时
  restudyNumZnjy: number // 智能记忆复习次数
  isEndZnjy: number // 智能记忆是否结束
}
