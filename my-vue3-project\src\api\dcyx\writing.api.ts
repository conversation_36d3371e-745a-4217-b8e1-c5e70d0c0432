import request from '@/utils/request'

export const DCYX_WRITING_API = {
  // 获取默写单词列表
  getWritingWords: (params: WritingWordsQuery) =>
    request<any, { list: WordItem[]; total: number }>({
      url: '/api/client/v1/word/page',
      method: 'get',
      params,
    }),

  // 更新默写学习记录
  updateWritingRecord: (data: WritingRecordUpdate) =>
    request<WritingRecord, any>({
      url: '/api/client/v1/user-study-record/update',
      method: 'post',
      data,
    }),

  // 获取默写学习记录
  getWritingRecords: (params: WritingRecordQuery) =>
    request<
      any,
      {
        list: WritingRecord[]
        total: number
        pageNum: number
        pageSize: number
      }
    >({
      url: '/api/client/v1/writing-record/page',
      method: 'get',
      params,
    }),

  // 更新单元默写状态
  updateUnitWritingStatus: (data: UnitWritingStatus) =>
    request<UnitWritingStatus, any>({
      url: '/api/client/v1/unit-study-status/update',
      method: 'post',
      data,
    }),
  // 更新单元默写统计
  updateUnitWritingStats: (data: UnitWritingStats) =>
    request<UnitWritingStats, any>({
      url: '/api/client/v1/unit-study-stats/update',
      method: 'post',
      data,
    }),

  // 获取单元默写状态
  getUnitWritingStatus: (params: UnitWritingStatusQuery) =>
    request<
      any,
      {
        list: UnitWritingStatus[]
        total: number
        pageNum: number
        pageSize: number
      }
    >({
      url: '/api/client/v1/unit-writing-status/page',
      method: 'get',
      params,
    }),
}

export interface WritingWordsQuery {
  unitId: number
  pageNum: number
  pageSize: number
}

export interface WritingRecordQuery {
  pageNum: number
  pageSize: number
  wordId?: number
  userId?: number
  unitId?: number
}

export interface UnitWritingStatusQuery {
  unitId: number
  userId: number
}

// 单词类型定义
export interface WordItem {
  id: string
  spelling: string
  syllable: string
  meaningZhCn: string
  exampleEnUs: string
  exampleZhCn: string
  unitName: string
}

// 默写记录
export interface WritingRecord {
  id: number
  userId: number
  wordId: number
  unitId: number
  unitName: string
  spelling: string
  userInput: string
  isCorrect: number // 1-正确 0-错误
  errorCount: number // 错误次数
  timeConsuming: number // 耗时(秒)
  source: string // 来源
  sourceFrom: string // 来源说明
  createTime: string
  updateTime: string
}

// 默写记录更新
export interface WritingRecordUpdate {
  wordId: number
  status: number
  source: string
  sourceFrom: string
}

// 单元默写状态
export interface UnitWritingStatus {
  unitId: number
  source: number
  writingCompleted: number
  writingScore: number
  writingTimeConsuming: number
}

// 单元默写统计
export interface UnitWritingStats {
  unitId: number
  unitTotal: number
  studyNumZnmx: number
  testTimeZnmx: number
  restudyNumZnmx: number
  isEndZnmx: number
}
