import request from '@/utils/request'

const USER_BASE_URL = '/api/client/v1/dcyx-users'

export const DCYX_USER_API = {
  /** 登录接口*/
  // login(data: any) {
  //   return request<any, any>({
  //     url: `${USER_BASE_URL}/student/studentH5Login`,
  //     method: 'post',
  //     data: data,
  //     headers: {
  //       'Content-Type': 'multipart/form-data',
  //     },
  //   })
  // },

  getUserInfo() {
    return request<any, DcyxUserInfo>({
      url: `${USER_BASE_URL}/me`,
      method: 'get',
    })
  },

  /** 获取学生信息 */
  getUserById(userid: string) {
    return request<any, any>({
      url: `${USER_BASE_URL}/${userid}/form`,
      method: 'get',
    })
  },
}

/**
 * 按照该实例注册 dcyxUserInfo 结构
 * {
    "id": "6",
    "organizationId": "5",
    "username": "admin",
    "password": "$2a$10$xVWsNOhHrCxh5UbpCE7/HuJ.PAOKcYAqRxD2CO2nVnJS.IAXkr5aq",
    "fullname": "系统管理员",
    "gender": 1,
    "mobile": "13700137001",
    "schoolName": null,
    "grade": null,
    "wechatId": null,
    "qq": null,
    "lastScore": 0,
    "expireTime": null,
    "loginTime": null,
    "email": "<EMAIL>",
    "grades": null,
    "studentType": 2,
    "firstLoginTime": null,
    "teacherName": null,
    "teacherMobile": null,
    "className": null,
    "status": 1
}
 */

export interface DcyxUserInfo {
  id: number
  organizationId: number
  username: string
  fullname: string
  gender: string
  mobile: string
  schoolName: string
  grade: string
  wechatId: string
  qq: string
  lastScore: string
  expireTime: string
  loginTime: string
  email: string
  grades: string
  extend: string
  studentType: string
  firstLoginTime: string
  teacherName: string
  teacherMobile: string
  className: string
}
