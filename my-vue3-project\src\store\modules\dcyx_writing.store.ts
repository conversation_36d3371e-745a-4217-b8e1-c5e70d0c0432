import { defineStore } from 'pinia'
import { DCYX_WRITING_API } from '@/api/dcyx/writing.api'
import type {
  WordItem,
  WritingRecord,
  UnitWritingStatus,
  WritingRecordUpdate,
} from '@/api/dcyx/writing.api'
import { useDcyxUserStoreHook } from './dcyx_user.store'

export const useDcyxWritingStore = defineStore('dcyx_writing', () => {
  const wordList = ref<WordItem[]>([])
  const unitWritingStatus = ref<UnitWritingStatus | null>(null)
  const writingRecords = ref<WritingRecord[]>([])
  const writingRecordsTotal = ref(0)

  // 获取默写单词列表
  const fetchWritingWords = async (unitId: number) => {
    return new Promise<WordItem[]>((resolve, reject) => {
      const params = {
        unitId,
        pageNum: 1,
        pageSize: 1000,
      }
      DCYX_WRITING_API.getWritingWords(params)
        .then((res) => {
          wordList.value = res.list
          resolve(res.list)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 更新默写学习记录
  const updateWritingRecord = async (data: WritingRecordUpdate) => {
    return new Promise<void>((resolve, reject) => {
      DCYX_WRITING_API.updateWritingRecord(data)
        .then(() => {
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 获取默写学习记录
  const fetchWritingRecords = async (params: {
    pageNum: number
    pageSize: number
    wordId?: number
    userId?: number
    unitId?: number
  }) => {
    return new Promise<{ list: WritingRecord[]; total: number }>(
      (resolve, reject) => {
        DCYX_WRITING_API.getWritingRecords(params)
          .then((res) => {
            writingRecords.value = res.list
            writingRecordsTotal.value = res.total
            resolve({ list: res.list, total: res.total })
          })
          .catch((error) => {
            reject(error)
          })
      },
    )
  }

  // 更新单元默写状态
  const updateUnitWritingStatus = async (data: UnitWritingStatus) => {
    return new Promise<void>((resolve, reject) => {
      DCYX_WRITING_API.updateUnitWritingStatus(data)
        .then(() => {
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 获取单元默写状态
  const fetchUnitWritingStatus = async (unitId: number) => {
    return new Promise<UnitWritingStatus | null>((resolve, reject) => {
      const userId = useDcyxUserStoreHook().userInfo.id
      const params = {
        unitId,
        userId,
      }
      DCYX_WRITING_API.getUnitWritingStatus(params)
        .then((res) => {
          const status = res.list && res.list.length > 0 ? res.list[0] : null
          unitWritingStatus.value = status
          resolve(status)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  return {
    wordList,
    unitWritingStatus,
    writingRecords,
    writingRecordsTotal,
    fetchWritingWords,
    updateWritingRecord,
    fetchWritingRecords,
    updateUnitWritingStatus,
    fetchUnitWritingStatus,
  }
})
