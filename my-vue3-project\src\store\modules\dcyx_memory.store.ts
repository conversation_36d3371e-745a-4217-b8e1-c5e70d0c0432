import { defineStore } from 'pinia'
import { DCYX_MEMORY_API } from '@/api/dcyx/memory.api'

import type {
  WordItem,
  UserWordStatus,
  UnitStudyStats,
  UserStudyRecord,
  UnitStudyStatus,
  UserWordStatusUpdate,
  UserStudyRecordUpdate,
} from '@/api/dcyx/memory.api'
import { useUserStoreHook } from './user.store'
import { useDcyxUserStoreHook } from './dcyx_user.store'
export const useDcyxMemoryStore = defineStore('dcyx_memory', () => {
  const wordList = ref<WordItem[]>([])
  const unitStudyStatus = ref<UnitStudyStatus | null>(null)

  // 获取单元单词列表
  const fetchWords = async (unitId: number) => {
    const params = {
      unitId,
      pageNum: 1,
      pageSize: 1000,
    }
    const res = await DCYX_MEMORY_API.getWordsByUnit(params)
    wordList.value = res.list
  }

  // 保存用户单词学习状态
  const saveUserWordStatus = async (data: UserWordStatus) => {
    await DCYX_MEMORY_API.saveUserWordStatus(data)
  }

  // 保存用户学习记录
  const saveUserStudyRecord = async (data: UserStudyRecord) => {
    await DCYX_MEMORY_API.saveUserStudyRecord(data)
  }

  // 保存单元-智能记忆学习状态
  const saveUnitStudyStatus = async (data: UnitStudyStatus) => {
    await DCYX_MEMORY_API.addUnitStudyStatus(data)
  }

  // 保存单元学习统计
  const saveUnitStudyStats = async (data: UnitStudyStats) => {
    await DCYX_MEMORY_API.addUnitStudyStats(data)
  }

  // 查询单元学习状态
  const fetchUnitStudyStatus = async (unitId: number) => {
    const userId = useDcyxUserStoreHook().userInfo.id
    const res = await DCYX_MEMORY_API.getUnitStudyStatus({
      unitId,
      userId: userId,
    })
    unitStudyStatus.value = res.list[0] || null
  }

  // 播放单词音频，返回 blob url
  const playWordAudio = async (word: string): Promise<string> => {
    const blob = await DCYX_MEMORY_API.getWordAudio(word)
    // 兼容 axios 返回 {data: Blob} 或直接 Blob
    const realBlob = blob instanceof Blob ? blob : blob.data
    return URL.createObjectURL(realBlob)
  }

  // 分页查询用户单词学习状态
  const userWordStatusList = ref<UserWordStatus[]>([])
  const userWordStatusTotal = ref(0)
  const fetchUserWordStatusPage = async (params: {
    pageNum: number
    pageSize: number
    wordId?: number
    userId?: number
  }) => {
    const res = await DCYX_MEMORY_API.getUserWordStatusPage(params)
    userWordStatusList.value = res.list
    userWordStatusTotal.value = res.total
  }

  // 分页查询用户学习记录
  const userStudyRecordList = ref<UserStudyRecord[]>([])
  const userStudyRecordTotal = ref(0)
  const fetchUserStudyRecordPage = async (params: {
    pageNum: number
    pageSize: number
    wordId?: number
    userId?: number
  }) => {
    const res = await DCYX_MEMORY_API.getUserStudyRecordPage(params)
    userStudyRecordList.value = res.list
    userStudyRecordTotal.value = res.total
  }

  // 更新用户单词学习状态
  const updateUserWordStatus = async (data: UserWordStatusUpdate) => {
    await DCYX_MEMORY_API.saveUserWordStatus(data)
  }

  // 更新用户学习记录
  const updateUserStudyRecord = async (data: UserStudyRecordUpdate) => {
    await DCYX_MEMORY_API.saveUserStudyRecord(data)
  }

  return {
    wordList,
    unitStudyStatus,
    fetchWords,
    saveUserWordStatus,
    saveUserStudyRecord,
    saveUnitStudyStatus,
    saveUnitStudyStats,
    fetchUnitStudyStatus,
    playWordAudio,
    userWordStatusList,
    userWordStatusTotal,
    fetchUserWordStatusPage,
    userStudyRecordList,
    userStudyRecordTotal,
    fetchUserStudyRecordPage,
    updateUserWordStatus,
    updateUserStudyRecord,
  }
})
