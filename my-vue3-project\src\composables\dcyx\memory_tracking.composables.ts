import { ref, computed, watch } from 'vue'
import { useDcyxMemoryTrackingStore } from '@/store/modules/dcyx_memory_tracking.store'
import { useDcyxProgramStore } from '@/store/modules/dcyx_program.store'
import { useDcyxUnitStore } from '@/store/modules/dcyx_unit.store'
import { ElMessage } from 'element-plus'

export const useMemoryTracking = () => {
  const memoryTrackingStore = useDcyxMemoryTrackingStore()
  const programStore = useDcyxProgramStore()
  const unitStore = useDcyxUnitStore()

  // 响应式数据
  const activeModule = ref<'memory' | 'dictation' | 'writing'>('memory')
  const selectedUnit = ref<string>('')

  // 计算属性
  const currentProgram = computed(() => programStore.currentProgram)
  const unitsList = computed(() => {
    const units = unitStore.unitsList || []
    return [
      { label: '全选', value: '' },
      ...units.map((unit) => ({
        label: unit.name,
        value: unit.id.toString(),
      })),
    ]
  })

  const sourceMap = computed(() => ({
    memory: 'znjy' as const,
    dictation: 'zntx' as const,
    writing: 'znmx' as const,
  }))

  const currentSource = computed(() => sourceMap.value[activeModule.value])

  const currentContent = computed(() => {
    const words = memoryTrackingStore.reviewWords
    if (words.length === 0) {
      return '暂无需要复习的单词'
    }
    return words.map((word) => word.spelling).join(', ')
  })

  // 监听器
  watch(activeModule, async (newModule) => {
    const source = sourceMap.value[newModule]
    memoryTrackingStore.setCurrentSource(source)
    await loadReviewWords()
  })

  watch(selectedUnit, async (newUnit) => {
    const unitId = newUnit ? parseInt(newUnit) : undefined
    memoryTrackingStore.setSelectedUnitId(unitId)
    await loadReviewWords()
  })

  // 方法
  const loadReviewWords = async () => {
    if (!currentProgram.value?.programId) {
      ElMessage.warning('请先选择课程')
      return
    }

    await memoryTrackingStore.loadReviewWords(currentProgram.value.programId)
  }

  const handleSmartReview = () => {
    if (memoryTrackingStore.needReviewCount === 0) {
      ElMessage.info('暂无需要复习的单词')
      return
    }
    ElMessage.success('开始智能复习')
    // TODO: 实现智能复习逻辑
  }

  const handleTaskReview = () => {
    if (memoryTrackingStore.totalWords === 0) {
      ElMessage.info('暂无学习记录')
      return
    }
    ElMessage.success('开始任务复习')
    // TODO: 实现任务复习逻辑
  }

  const handleDownload = () => {
    if (memoryTrackingStore.totalWords === 0) {
      ElMessage.info('暂无学习记录可下载')
      return
    }
    ElMessage.success('开始下载学习资料')
    // TODO: 实现下载逻辑
  }

  const handleStartLearning = () => {
    if (memoryTrackingStore.totalWords === 0) {
      ElMessage.info('暂无学习内容')
      return
    }
    ElMessage.success('开始学习当前内容')
    // TODO: 实现开始学习逻辑
  }

  const handlePreview = () => {
    if (memoryTrackingStore.totalWords === 0) {
      ElMessage.info('暂无学习内容可预览')
      return
    }
    ElMessage.info('预览学习内容')
    // TODO: 实现预览逻辑
  }

  const initialize = async () => {
    await programStore.fetchCurrentProgram()
    // TODO: 需要实现加载单元列表的方法
    await loadReviewWords()
  }

  return {
    // 状态
    activeModule,
    selectedUnit,

    // 计算属性
    currentProgram,
    unitsList,
    currentSource,
    currentContent,
    totalWords: memoryTrackingStore.totalWords,
    needReviewCount: memoryTrackingStore.needReviewCount,
    loading: memoryTrackingStore.loading,
    reviewWords: memoryTrackingStore.reviewWords,

    // 方法
    loadReviewWords,
    handleSmartReview,
    handleTaskReview,
    handleDownload,
    handleStartLearning,
    handlePreview,
    initialize,
  }
}
