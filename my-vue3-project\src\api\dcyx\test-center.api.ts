import request from '@/utils/request'

const TEST_CENTER_BASE_URL = '/api/client/v1'

const DCYX_TEST_CENTER_API = {
  /**
   * 获取教材单元测试列表
   * @param params 查询参数
   */
  getUnitTestList: (params: UnitTestListQueryParams) => {
    return request<UnitTestListQueryParams, PageResult<UnitTestItem[]>>({
      url: `${TEST_CENTER_BASE_URL}/test/unit-tests`,
      method: 'get',
      params,
    })
  },

  /**
   * 获取测试记录列表
   * @param params 查询参数
   */
  getTestRecordList: (params: TestRecordListQueryParams) => {
    return request<TestRecordListQueryParams, PageResult<TestRecordItem[]>>({
      url: `${TEST_CENTER_BASE_URL}/unit-test-record/page`,
      method: 'get',
      params,
    })
  },

  /**
   * 开始单元测试
   * @param data 测试参数
   */
  startUnitTest: (data: StartUnitTestPayload) => {
    return request<StartUnitTestPayload, StartUnitTestResponse>({
      url: `${TEST_CENTER_BASE_URL}/start-test`,
      method: 'post',
      data,
    })
  },

  /**
   * 获取测试详情
   * @param testRecordId 测试记录ID
   */
  getTestDetail: (testRecordId: number) => {
    return request<any, TestDetailItem>({
      url: `${TEST_CENTER_BASE_URL}/test-detail/${testRecordId}`,
      method: 'get',
    })
  },
}

export default DCYX_TEST_CENTER_API

// 单元测试列表查询参数
export interface UnitTestListQueryParams extends PageQuery {
  programId: number
}

// 测试记录列表查询参数
export interface TestRecordListQueryParams extends PageQuery {
  programId: number
  unitId?: number
}

/**
 *
 */
// 单元测试项
export interface UnitTestItem {
  id: number
  unitId: number
  unitName: string
  unitIndex: number
  programId: number
  programName: string
  seriesId: number
  totalWords: number
  isCompleted: number // 0-未完成测试, 1-已完成测试
  lastTestScore?: number // 最后一次测试分数
  lastTestTime?: string // 最后一次测试时间
  testCount: number // 测试次数
}

// 测试记录项
export interface TestRecordItem {
  id: number
  unitId: number
  unitName: string
  programId: number
  programName: string
  seriesId: number
  testTime: string
  testScore: number
  testComment: string
  testDuration: number // 测试用时(秒)
  totalQuestions: number // 总题数
  correctAnswers: number // 正确答案数
  wrongAnswers: number // 错误答案数
  userId: number
}

// 开始测试参数
export interface StartUnitTestPayload {
  unitId: number
  programId: number
  seriesId: number
}

// 开始测试响应
export interface StartUnitTestResponse {
  testId: number
  testUrl: string
  message: string
}

// 测试详情项
export interface TestDetailItem {
  id: number
  unitId: number
  unitName: string
  programId: number
  programName: string
  testTime: string
  testScore: number
  testComment: string
  testDuration: number
  totalQuestions: number
  correctAnswers: number
  wrongAnswers: number
  errorWords: ErrorWordItem[] // 错误单词列表
  errorQuestionTypes: ErrorQuestionTypeItem[] // 错误题目类型列表
}

// 错误单词项
export interface ErrorWordItem {
  id: number
  wordId: number
  spelling: string
  meaningZhCn: string
  questionType: string // 题目类型: 'memory'|'dictation'|'writing'
  userAnswer: string
  correctAnswer: string
}

// 错误题目类型项
export interface ErrorQuestionTypeItem {
  questionType: string // 题目类型
  questionTypeName: string // 题目类型名称
  errorCount: number // 错误数量
  totalCount: number // 总数量
  errorRate: number // 错误率
}
