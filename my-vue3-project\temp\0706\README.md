
#### todo01
背景：
1. 已经完成了智能记忆学习功能和页面，位于 src\views\dcyx\learn-main\inteli-memory.vue；

需求：
1. 完成智能听写和智能默写；
2. 完成智能听写功能，位于 src\views\dcyx\learn-main\inteli-dictation.vue ；
3. 完成智能默写功能，位于  src\views\dcyx\learn-main\inteli-writing.vue；
4. 进行稳定性强、代码结构清晰、可读性强的实现、合理的组织代码函数、结构、模块；
5. 实现对于的 API、STORE、VIEW；
  1. API 文件定义reques 请求函数和对象结构，使用 interface 定义对象结构；
  2. STORE 中，定义合理的变量，函数使用 new Promise 代码风格，包装一个 API 调用；
  3. VIEW 中，对于 STORE 函数的调用使用try-catch 代码风格包裹；


#### todo02
背景：
1. 已经完成了 todo01的大部分，只有 智能默写的 VIEW 没有完成；
需求：
1. 完成  智能默写的 VIEW ；

#### todo03
背景：
1. 单词数量信息，从 src\store\modules\dcyx_unit.store.ts  unitsList 变量中获取；
需求
1. 单词训练主界面，src\views\dcyx\work-main\word-training.vue
2. 在 currentUnitStudyStats 变量不存在时，也要显示智能记忆卡片、智能听写卡片、智能默写卡片；
  1. 都显示 '开始学习' 字样，单词数量；