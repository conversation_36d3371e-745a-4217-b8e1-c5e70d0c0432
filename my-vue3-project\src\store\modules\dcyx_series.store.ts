// import { defineStore } from 'pinia'
import { ref } from 'vue'
import DCYX_SERIES_API from '@/api/dcyx/series.api'
import type { Semester, Series } from '@/api/dcyx/series.api'
import { store } from '@/store'

export const useDcyxSeriesStore = defineStore('dcyx_series', () => {
  // const dcyxUnitStore = useDcyxUnitStore()
  // 学期列表
  const semesterList = ref<Semester[]>([])
  // 系列列表
  const serieList = ref<Series[]>([])
  const loading = ref(false)
  const inited = ref(false)
  const currentSemesterId = ref<number>(0)
  const currentSeriesId = ref<number>(0)

  // const currentSemesterId = computed(
  //   () => dcyxUnitStore.currentUnit?.semesterId,
  // )
  // const currentSeriesId = computed(() => dcyxUnitStore.currentUnit?.seriesId)

  // setcurrentSemesterId、setcurrentSeriesId
  function setCurrentSemesterId(semesterId: number) {
    currentSemesterId.value = semesterId
  }
  function setCurrentSeriesId(seriesId: number) {
    currentSeriesId.value = seriesId
  }

  // 获取学期数据
  const fetchSemesterList = () => {
    return new Promise<Semester[]>((resolve, reject) => {
      DCYX_SERIES_API.getSemesterList({
        pageNum: 1,
        pageSize: 10,
      })
        .then((res) => {
          const list = res.list || []
          semesterList.value = list
          // if (list.length > 0) {
          //   currentSemesterId.value = list[0].id
          // }
          resolve(list)
        })
        .catch((error) => {
          console.error('获取学期列表失败', error)
          semesterList.value = []
          reject(error)
        })
    })
  }

  // 获取教材系列数据
  // const getSeriesByUser = async () => {
  //   return new Promise<series[]>((resolve, reject) => {
  //     DCYX_SERIES_API.getSeriesByUser()
  //       .then((res) => {
  //         serieList.value = res || []
  //         resolve(res)
  //       })
  //       .catch((error) => {
  //         reject(error)
  //       })
  //   })
  // }

  const fetchSeriesBySemesterId = (semesterId: number) => {
    return new Promise<Series[]>((resolve, reject) => {
      DCYX_SERIES_API.getSeriesBySemesterId({
        pageNum: 1,
        pageSize: 10,
        semesterId,
      })
        .then((res) => {
          const list = res.list || []
          serieList.value = list
          resolve(list)
        })
        .catch((error) => {
          console.error('获取系列列表失败', error)
          serieList.value = []
          reject(error)
        })
    })
  }

  const initSeriesStore = async () => {
    if (inited.value) return

    await fetchSemesterList()
    if (currentSemesterId.value) {
      await fetchSeriesBySemesterId(currentSemesterId.value)
      inited.value = true
      console.log('currentSemesterId', currentSemesterId.value)
      console.log('currentSeriesId', currentSeriesId.value)
    }
  }

  /**
   * 清空 series store
   */
  function resetSeriesStore() {
    semesterList.value = []
    serieList.value = []
    currentSemesterId.value = 0
    currentSeriesId.value = 0
    inited.value = false
    // 其他需要清空的状态可在此添加
  }

  return {
    semesterList,
    serieList,
    currentSemesterId,
    currentSeriesId,
    loading,
    fetchSemesterList,
    // getSeriesByUser,
    fetchSeriesBySemesterId,
    initSeriesStore,
    setCurrentSemesterId,
    setCurrentSeriesId,
    resetSeriesStore,
  }
})

export function useDcyxSeriesStoreHook() {
  return useDcyxSeriesStore(store)
}
