<template>
  <div class="right-board">
    <el-card class="right-card" shadow="hover">
      <!-- 当前教材项 -->
      <div class="program-section">
        <div class="program-title">当前教材</div>
        <div v-if="inited" class="program-info" @click="onEnterProgram">
          <div>
            教材名称:
            <span>{{ localProgram.programName }}</span>
          </div>
          <div>
            系列:
            <span>{{ localProgram.seriesName }}</span>
          </div>
          <div>
            学期:
            <span>{{ localProgram.semesterName }}</span>
          </div>
          <el-progress
            :text-inside="true"
            :percentage="
              Number(
                (
                  localProgram.programStudyWordNumber /
                  localProgram.programWordNumber
                ).toFixed(3),
              ) * 100
            "
            :stroke-width="18"
            status="success"
          />

          <div class="progress-info">
            <span>已学{{ localProgram.programStudyWordNumber }}个，</span>
            <span>共{{ localProgram.programWordNumber }}个</span>
          </div>
        </div>

        <!-- 点击打开教材选择项 -->
        <div v-else class="program-info-empty" @click="onOpenProgramSelect">
          请选择学习的教材项
        </div>
      </div>

      <!-- 发布任务 -->
      <div class="task-section">
        <div class="task-title">发布任务</div>
      </div>

      <!-- 今日词汇统计 -->
      <div class="vocab-section">
        <div class="vocab-title">今日词汇统计</div>
        <div class="vocab-info">
          <div>
            记忆时长: <span>{{ memoryDuration }}</span>
          </div>
          <div>
            数量: <span>{{ vocabCount }}</span>
          </div>
          <div>
            速度: <span>{{ speed }}</span> 个/小时
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

defineOptions({
  name: 'DcyxSysRightBoard',
  inheritAttrs: false,
})

const emits = defineEmits<{
  (_e: 'enter-program'): void
  (_e: 'open-program-select'): void
}>()

import { useDcyxProgramStore } from '@/store'
import { useDcyxStudyStore } from '@/store'
import type { CurrentProgram } from '@/api/dcyx/program.api'

const dcyxProgramStore = useDcyxProgramStore()
const dcyxStudyStore = useDcyxStudyStore()

const localProgram = ref({} as CurrentProgram)
const inited = ref(false)
const taskCount = ref(0)
const memoryDuration = ref('00:00')
const vocabCount = ref(0)
const speed = ref(0)

function initTaskInfo() {
  taskCount.value = 12
}

async function initTodayVocabInfo() {
  try {
    const report = await dcyxStudyStore.fetchTodayVocabReport()
    // 假设 studyDuration 单位为分钟
    const hours = Math.floor(report.studyDuration / 60)
    const minutes = report.studyDuration % 60
    memoryDuration.value = [hours, minutes]
      .map((num) => String(num).padStart(2, '0'))
      .join(':')
    vocabCount.value = report.wordCount
    speed.value = report.studySpeed
  } catch {
    memoryDuration.value = '00:00'
    vocabCount.value = 0
    speed.value = 0
  }
}

function onEnterProgram() {
  emits('enter-program')
}

function onOpenProgramSelect() {
  emits('open-program-select')
}

onMounted(async () => {
  try {
    initTaskInfo()
    await initTodayVocabInfo()
    if (!dcyxProgramStore.inited) {
      await dcyxProgramStore.initProgramStore()
    }
    localProgram.value = { ...dcyxProgramStore.currentProgram }
    inited.value = true
  } catch (error) {
    console.error('DcyxSysRightBoard 组件 onMounted 初始化失败', error)
  }
})

onBeforeUnmount(() => {
  dcyxProgramStore.resetProgramStore()
})
</script>

<style scoped lang="scss">
.right-board {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  // background: linear-gradient(135deg, #fdf6e3 0%, #f5f7fa 100%);
  // background: linear-gradient(135deg, #e0eafc 0%, #cfdef3 100%);
}
.right-card {
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
  color: #333;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 20px 16px;
  gap: 16px;
}
.progress-section {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}
.course-title {
  font-size: 16px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 8px;
}
.task-section {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}
.task-title {
  font-size: 14px;
  font-weight: bold;
  color: #f57c00;
  margin-top: 4px;
}
.unit-info {
  font-size: 14px;
  color: #333;
  margin-top: 8px;
}
.unit-info-empty {
  font-size: 14px;
  color: #e6a23c;
  margin-top: 8px;
}
.vocab-section {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}
.vocab-title {
  font-size: 14px;
  font-weight: bold;
  color: #43e97b;
  margin-bottom: 8px;
}
.vocab-info {
  font-size: 13px;
  color: #2d3a4b;
  div {
    margin-bottom: 4px;
    span {
      font-weight: bold;
      color: #1976d2;
    }
  }
}
.progress-section .el-progress--line {
  margin-bottom: 12px;
  max-width: 100%;
}
.program-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
}
.program-title {
  font-size: 16px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 8px;
}
.program-info {
  font-size: 14px;
  color: #333;
  div {
    margin-bottom: 4px;
    span {
      font-weight: bold;
    }
  }
}
.program-info-empty {
  font-size: 14px;
  color: #e6a23c;
  margin-top: 8px;
}
</style>
