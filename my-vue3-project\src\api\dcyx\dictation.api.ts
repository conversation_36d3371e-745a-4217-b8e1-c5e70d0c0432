import request from '@/utils/request'

export const DCYX_DICTATION_API = {
  // 获取听写单词列表
  getDictationWords: (params: DictationWordsQuery) =>
    request<any, { list: WordItem[]; total: number }>({
      url: '/api/client/v1/word/page',
      method: 'get',
      params,
    }),

  // 更新听写学习记录
  updateDictationRecord: (data: DictationRecordUpdate) =>
    request<DictationRecord, any>({
      url: '/api/client/v1/user-study-record/update',
      method: 'post',
      data,
    }),

  // 获取听写学习记录
  getDictationRecords: (params: DictationRecordQuery) =>
    request<
      any,
      {
        list: DictationRecord[]
        total: number
        pageNum: number
        pageSize: number
      }
    >({
      url: '/api/client/v1/dictation-record/page',
      method: 'get',
      params,
    }),

  // 更新单元听写状态
  updateUnitDictationStatus: (data: UnitDictationStatus) =>
    request<UnitDictationStatus, any>({
      url: '/api/client/v1/unit-study-status/update',
      method: 'post',
      data,
    }),
  // 更新单元听写统计
  updateUnitDictationStats: (data: UnitDictationStats) =>
    request<UnitDictationStatus, any>({
      url: '/api/client/v1/unit-study-stats/update',
      method: 'post',
      data,
    }),

  // 获取单元听写状态
  getUnitDictationStatus: (params: UnitDictationStatusQuery) =>
    request<
      any,
      {
        list: UnitDictationStatus[]
        total: number
        pageNum: number
        pageSize: number
      }
    >({
      url: '/api/client/v1/unit-dictation-status/page',
      method: 'get',
      params,
    }),
}

export interface DictationWordsQuery {
  unitId: number
  pageNum: number
  pageSize: number
}

export interface DictationRecordQuery {
  pageNum: number
  pageSize: number
  wordId?: number
  userId?: number
  unitId?: number
}

export interface UnitDictationStatusQuery {
  unitId: number
  userId: number
}

// 单词类型定义
export interface WordItem {
  id: string
  spelling: string
  syllable: string
  meaningZhCn: string
  exampleEnUs: string
  exampleZhCn: string
  unitName: string
}

// 听写记录
export interface DictationRecord {
  id: number
  userId: number
  wordId: number
  unitId: number
  unitName: string
  spelling: string
  userInput: string
  isCorrect: number // 1-正确 0-错误
  errorCount: number // 错误次数
  timeConsuming: number // 耗时(秒)
  source: string // 来源
  sourceFrom: string // 来源说明
  createTime: string
  updateTime: string
}

// 听写记录更新
/**
 * {
  "wordId": 1,
  "status": 1,
  "source": "znjy",
  "sourceFrom": "znjy"
}
 */
export interface DictationRecordUpdate {
  wordId: number
  status: number
  source: string
  sourceFrom: string
}

/**
 * {
  "unitId": 1,
  "source": 2,
  "memoryCompleted": 1,
  "memoryScore": 33,
  "memoryTimeConsuming": 3000,
  "dictationCompleted": 1,
  "dictationScore": 44,
  "dictationTimeConsuming": 4000,
  "writingCompleted": 1,
  "writingScore": 66,
  "writingTimeConsuming": 6000
  }
 */
// 单元听写状态
export interface UnitDictationStatus {
  unitId: number
  source: number
  dictationCompleted: number
  dictationScore: number
  dictationTimeConsuming: number
}

/**
 * {
  "unitId": 1,
  "unitTotal": 10,
  "studyNumZnjy": 8,
  "studyNumZnmx": 0,
  "studyNumZntx": 0,
  "testTimeZnjy": 1000,
  "testTimeZnmx": 0,
  "testTimeZntx": 0,
  "restudyNumZnjy": 2,
  "restudyNumZnmx": 0,
  "restudyNumZntx": 0,
  "isEndZnjy": 1,
  "isEndZnmx": 0,
  "isEndZntx": 0
}
 */
// 单元听写统计
export interface UnitDictationStats {
  unitId: number
  unitTotal: number
  studyNumZntx: number
  testTimeZntx: number
  restudyNumZntx: number
  isEndZntx: number
}
