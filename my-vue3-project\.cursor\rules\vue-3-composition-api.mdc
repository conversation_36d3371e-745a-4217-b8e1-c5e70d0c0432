---
description: 
globs: 
alwaysApply: true
---


# Vue 3 (Composition API)

# Vue 3 Composition API .cursorrules

## Vue 3 Composition API Best Practices

```javascript
const vue3CompositionApiBestPractices = [
  "Use setup() function for component logic",
  "Utilize ref and reactive for reactive state",
  "Implement computed properties with computed()",
  "Use watch and watchEffect for side effects",
  "Implement lifecycle hooks with onMounted, onUpdated, etc.",
  "Utilize provide/inject for dependency injection",
];
```

## Folder Structure

```javascript
const folderStructure = `
src/
  components/
  composables/
  views/
  router/
  store/
  assets/
  App.vue
  main.js
`;
```

## Additional Instructions

```javascript
const additionalInstructions = `
1. Use TypeScript for type safety
2. Implement proper props and emits definitions
3. Utilize Vue 3's Teleport component when needed
4. Use Suspense for async components
5. Implement proper error handling
6. Follow Vue 3 style guide and naming conventions
7. Use Vite for fast development and building
`;
```

### Detailed Instructions

1. **TypeScript Integration**: Ensure all components and composables are written in TypeScript to leverage type safety and improve code quality.
2. **Props and Emits**: Define props and emits using TypeScript interfaces to ensure type safety and clarity in component communication.
3. **Teleport Component**: Use Vue 3's Teleport component to render components outside the current DOM hierarchy when necessary.
4. **Suspense for Async Components**: Implement Suspense to handle asynchronous components and provide a better user experience.
5. **Error Handling**: Implement global error handling using Vue 3's error handling mechanisms to catch and handle errors gracefully.
6. **Style Guide and Naming Conventions**: Follow the official Vue 3 style guide and naming conventions to maintain consistency across the codebase.
7. **Vite for Development**: Use Vite as the build tool for faster development and building processes.

### Example Usage

```javascript
// Example of a Vue 3 component using Composition API
import { ref, computed, onMounted } from 'vue';

export default {
  setup() {
    const count = ref(0);
    const doubleCount = computed(() => count.value * 2);

    onMounted(() => {
      console.log('Component mounted');
    });

    return {
      count,
      doubleCount,
    };
  },
};
```

### Conclusion

By following these best practices and guidelines, you can build scalable, maintainable, and efficient Vue 3 applications using the Composition API. Always refer to the official Vue 3 documentation for the latest updates and features.
