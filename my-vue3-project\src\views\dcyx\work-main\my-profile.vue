<template>
  <div class="my-profile">
    <div class="my-profile__header">
      <div class="profile-card">
        <div class="profile-card__avatar">
          <el-avatar :size="80" src="https://placeholder.com/150" />
        </div>
        <div class="profile-card__info">
          <h2 class="profile-card__name">
            {{ userInfo.fullname || userInfo.username }}
          </h2>
          <!-- <p class="profile-card__bio">
            {{ getStudentTypeText(userInfo.studentType) }}
          </p> -->
          <div class="profile-card__stats">
            <div class="stat-item">
              <span class="stat-value">{{ userInfo.lastScore || '0' }}</span>
              <span class="stat-label">最新成绩</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ userInfo.grade || '未设置' }}</span>
              <span class="stat-label">年级</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{
                userInfo.className || '未设置'
              }}</span>
              <span class="stat-label">班级</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="my-profile__content">
      <el-tabs v-model="activeTab" class="profile-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <div class="info-section">
            <el-card class="info-card" shadow="hover">
              <template #header>
                <div class="info-card__header">
                  <el-icon :size="24" color="var(--el-color-primary)">
                    <User />
                  </el-icon>
                  <span>基本信息</span>
                </div>
              </template>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">用户ID</span>
                  <span class="info-value">{{ userInfo.id }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">用户名</span>
                  <span class="info-value">{{ userInfo.username }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">姓名</span>
                  <span class="info-value">{{ userInfo.fullname }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">性别</span>
                  <span class="info-value">{{
                    getGenderText(userInfo.gender)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">手机号</span>
                  <span class="info-value">{{
                    userInfo.mobile || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">邮箱</span>
                  <span class="info-value">{{
                    userInfo.email || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">微信ID</span>
                  <span class="info-value">{{
                    userInfo.wechatId || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">QQ</span>
                  <span class="info-value">{{ userInfo.qq || '未设置' }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane label="学习信息" name="study">
          <div class="info-section">
            <el-card class="info-card" shadow="hover">
              <template #header>
                <div class="info-card__header">
                  <el-icon :size="24" color="var(--el-color-success)">
                    <School />
                  </el-icon>
                  <span>学习信息</span>
                </div>
              </template>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">学校名称</span>
                  <span class="info-value">{{
                    userInfo.schoolName || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">年级</span>
                  <span class="info-value">{{
                    userInfo.grade || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">班级</span>
                  <span class="info-value">{{
                    userInfo.className || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">学生类型</span>
                  <span class="info-value">{{
                    getStudentTypeText(userInfo.studentType)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">最新成绩</span>
                  <span class="info-value">{{
                    userInfo.lastScore || '0'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">成绩等级</span>
                  <span class="info-value">{{
                    userInfo.grades || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">扩展信息</span>
                  <span class="info-value">{{ userInfo.extend || '无' }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane label="教师信息" name="teacher">
          <div class="info-section">
            <el-card class="info-card" shadow="hover">
              <template #header>
                <div class="info-card__header">
                  <el-icon :size="24" color="var(--el-color-warning)">
                    <UserFilled />
                  </el-icon>
                  <span>教师信息</span>
                </div>
              </template>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">教师姓名</span>
                  <span class="info-value">{{
                    userInfo.teacherName || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">教师手机号</span>
                  <span class="info-value">{{
                    userInfo.teacherMobile || '未设置'
                  }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane label="时间信息" name="time">
          <div class="info-section">
            <el-card class="info-card" shadow="hover">
              <template #header>
                <div class="info-card__header">
                  <el-icon :size="24" color="var(--el-color-info)">
                    <Clock />
                  </el-icon>
                  <span>时间信息</span>
                </div>
              </template>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">首次登录时间</span>
                  <span class="info-value">{{
                    formatTime(userInfo.firstLoginTime)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">最后登录时间</span>
                  <span class="info-value">{{
                    formatTime(userInfo.loginTime)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">过期时间</span>
                  <span class="info-value">{{
                    formatTime(userInfo.expireTime)
                  }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { User, School, UserFilled, Clock } from '@element-plus/icons-vue'
import { DCYX_USER_API, type DcyxUserInfo } from '@/api/dcyx/user.api'
import { ElMessage } from 'element-plus'

const activeTab = ref('basic')
const userInfo = ref<DcyxUserInfo>({
  id: 0,
  organizationId: 0,
  username: '',
  fullname: '',
  gender: '',
  mobile: '',
  schoolName: '',
  grade: '',
  wechatId: '',
  qq: '',
  lastScore: '',
  expireTime: '',
  loginTime: '',
  email: '',
  grades: '',
  extend: '',
  studentType: '',
  firstLoginTime: '',
  teacherName: '',
  teacherMobile: '',
  className: '',
})

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const data = await DCYX_USER_API.getUserInfo()
    if (data) {
      userInfo.value = data
    }
  } catch (error) {
    ElMessage.error('获取用户信息失败')
    console.error('获取用户信息失败:', error)
  }
}

// 格式化性别显示
const getGenderText = (gender: string) => {
  switch (gender) {
    case '1':
      return '男'
    case '2':
      return '女'
    default:
      return '未设置'
  }
}

// 格式化学生类型显示
const getStudentTypeText = (studentType: string) => {
  switch (studentType) {
    case '1':
      return '小学生'
    case '2':
      return '中学生'
    case '3':
      return '高中生'
    default:
      return '未设置'
  }
}

// 格式化时间显示
const formatTime = (time: string) => {
  if (!time) return '未设置'
  try {
    return new Date(time).toLocaleString('zh-CN')
  } catch {
    return time
  }
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.my-profile {
  padding: 20px;
  height: 100%;
  background-color: var(--el-bg-color-page);

  &__header {
    margin-bottom: 24px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

.profile-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  gap: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &__avatar {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  &__info {
    flex: 1;
  }

  &__name {
    margin: 0 0 8px;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  &__bio {
    margin: 0 0 16px;
    color: var(--el-text-color-regular);
  }

  &__stats {
    display: flex;
    gap: 32px;
  }
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;

  .stat-value {
    font-size: 20px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .stat-label {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.profile-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  &__header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);

  .info-label {
    font-weight: 500;
    color: var(--el-text-color-regular);
    min-width: 100px;
  }

  .info-value {
    font-weight: 600;
    color: var(--el-text-color-primary);
    text-align: right;
    flex: 1;
    margin-left: 16px;
  }
}
</style>
