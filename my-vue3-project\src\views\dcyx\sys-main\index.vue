<template>
  <div class="sys-main">
    <!-- 顶部导航栏 -->
    <dcyx-nav-bar
      ref="dcyxNavBarRef"
      @logout="logout"
      @nav-select="onNavSelect"
      @semester-change="onSemesterChange"
    />
    <div class="sys-main__container">
      <!-- 左侧功能区 -->
      <div class="sys-main__sidebar">
        <dcyx-sys-left-board
          @smart-review="onSmartReview"
          @daily-task="onDailyTask"
          @test-center="onTestCenter"
          @profile="onProfile"
        />
      </div>
      <!-- 右侧信息区 -->
      <div class="sys-main__main">
        <dcyx-sys-right-board
          @enter-program="onEnterProgram"
          @open-program-select="onOpenProgramSelect"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SysMain',
  inheritAttrs: false,
})
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import DcyxNavBar from './compoments/DcyxNavBar/index.vue'
import DcyxSysLeftBoard from './compoments/DcyxSysLeftBoard/index.vue'
import DcyxSysRightBoard from './compoments/DcyxSysRightBoard/index.vue'
import {
  useDcyxSeriesStore,
  useDcyxUserStore,
  useDcyxProgramStore,
  useDcyxUnitStore,
} from '@/store'

const dcyxNavBarRef = ref<InstanceType<typeof DcyxNavBar> | null>(null)
const userStore = useDcyxUserStore()
const router = useRouter()
const dcyxSeriesStore = useDcyxSeriesStore()
const dcyxProgramStore = useDcyxProgramStore()
const dcyxUnitStore = useDcyxUnitStore()

// 本地变量
const localUnit = ref()
const localProgram = ref()
const localSeries = ref()
const localUser = ref()

// 退出登录
async function logout() {
  try {
    await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    userStore.userInfo = {} as any
    dcyxProgramStore.resetProgramStore()
    dcyxUnitStore.resetUnitStore()
    ElMessage({ type: 'success', message: '已退出登录' })
    router.push('/login')
  } catch (error) {
    if (error === 'cancel') {
      ElMessage({ type: 'info', message: '取消登出' })
    } else {
      console.error('logout func error', error)
    }
  }
}
function onNavSelect(_key: string) {}
function onSemesterChange(_value: string) {}
function onSmartReview() {
  ElMessage.success('点击了智能复习')
  router.push('/work/word-training')
}
function onDailyTask() {
  ElMessage.info('点击了每日任务')
}
function onTestCenter() {
  ElMessage.info('点击了测试中心')
  router.push('/work/test-center')
}
function onProfile() {
  ElMessage.info('点击了我的')
  router.push('/work/my-profile')
}
function onEnterProgram() {
  ElMessage.info('点击了进入课程')
  router.push('/work/word-training')
}
function onOpenProgramSelect() {
  dcyxNavBarRef.value?.openSelectStudyModuleDialog()
}

const initDashboard = async () => {
  try {
    await dcyxUnitStore.initUnitStore()
    localUnit.value = { ...dcyxUnitStore.currentUnit }
    await dcyxProgramStore.initProgramStore()
    localProgram.value = { ...dcyxProgramStore.currentProgram }
    await dcyxSeriesStore.initSeriesStore()
    localSeries.value = [...dcyxSeriesStore.serieList]
    localUser.value = { ...userStore.userInfo }
  } catch (error) {
    console.error('initDashboard - 初始化 Dashboard 失败', error)
    ElMessage.error('初始化应用数据失败，请稍后重试')
  }
}
onMounted(() => {
  console.log('sys-main onMounted')
  initDashboard()
})
onBeforeUnmount(() => {
  dcyxUnitStore.resetUnitStore()
  dcyxProgramStore.resetProgramStore()
  dcyxSeriesStore.resetSeriesStore()
  // userStore 没有 reset 方法，如有需要可补充
})
</script>

<style lang="scss" scoped>
.sys-main {
  height: 100vh;
  background-color: var(--el-bg-color-page);
  display: flex;
  flex-direction: column;
}
.sys-main__container {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px 24px 0 24px;
  box-sizing: border-box;
}
.sys-main__sidebar {
  width: 320px;
  min-width: 260px;
  max-width: 400px;
  height: calc(100vh - 64px - 16px);
  /* 顶部导航64px+padding16px */
  background: none;
  display: flex;
  flex-direction: column;
}
.sys-main__main {
  flex: 1;
  min-width: 0;
  height: calc(100vh - 64px - 16px);
  display: flex;
  flex-direction: column;
  background: none;
}
</style>
