
#### todo01
背景：
1. 单词训练页面， 调用的 API 需要调整，位于 my-vue3-project/src/views/dcyx/work-main/word-training.vue
2. API 信息：
/api/client/v1/unit/page
参数传入 programId 
响应
{
	"code": "",
	"data": {
		"list": [
			{
				"id": 0,
				"seriesId": 0,
				"programId": 0,
				"programName": "",
				"name": "",
				"nameEnUs": "",
				"nameZhCn": "",
				"nameZhBig": "",
				"unitIndex": 0,
				"createUser": 0,
				"updateUser": 0,
				"status": 0,
				"totalNum": 0
			}
		],
		"total": 0
	},
	"msg": ""
}

需求：
1. 根据 dcyxProgramStore.currentProgram.programId ，查询单元列表信息，保存到该页面的一个 ref 变量中；并在视图中显示出来；


#### todo01