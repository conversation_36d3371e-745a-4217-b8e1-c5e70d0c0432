#### todo01
背景：
1. 单词学习界面 src\views\dcyx\work-main\word-training.vue
2. 当前选中单元的API /api/client/v1/user-current-unit/current
  这是返回示例；
  {
		"id": "1",
		"userId": "1",
		"username": "z<PERSON><PERSON>",
		"semesterId": "1",
		"seriesId": "1",
		"programId": "1",
		"wordId": "1",
		"wordIdx": 1,
		"groupId": "g1",
		"fortify": "f1",
		"unitId": "1",
		"unitName": "Unit 1: Greetings",
		"source": "app",
		"errors": 0,
		"rights": 5,
		"isEnd": 0,
		"errorWordInfo": null
	},
3. 单元的三种学习模块信息 API 为 /api/client/v1/unit-study-stats/page
  分页查询，支持参数：unitId、pageNum、pageSize
  返回示例：
  {
		"list": [
			{
				"id": "1",
				"userId": "1",
				"unitId": "1",
				"unitTotal": 12,
				"studyNumZnjy": 8,
				"studyNumZnmx": 5,
				"studyNumZntx": 7,
				"testTimeZnjy": 3,
				"testTimeZnmx": 2,
				"testTimeZntx": 4,
				"restudyNumZnjy": 2,
				"restudyNumZnmx": 1,
				"restudyNumZntx": 3,
				"isEndZnjy": 1,
				"isEndZnmx": 0,
				"isEndZntx": 1
			},]}
4. 更喜欢稳定性强的代码实现；
5. 尽可能保留原有功能；


需求：
1. 开发功能，在单词学习界面，
  1. 进入该界面，在左侧菜单展示单元列表；
  2. 默认选中当前选中的单元，根据当前选中单元的API查询出当前选中单元的ID；
  3. 如果查询结果为 null，则选中首个单元；
  4. 右侧面板会展示该单元对应的三种学习模块的状态信息，以卡片形式展示，信息通过 API 查询；
    1. 展示 unitTotal 该单元的单词总数；
    1. 智能记忆的学习个数、需要复习单词个数、测试时长、是否完成、对应 studyNumZnjy、restudyNumZnjy、testTimeZnjy、isEndZnjy
    2. 智能听写的学习个数、需要复习单词个数、测试时长、是否完成、对应 studyNumZntx、restudyNumZntx、testTimeZntx、isEndZntx
    3. 智能默写的学习个数、需要复习单词个数、测试时长、是否完成、对应 studyNumZnmx、restudyNumZnmx、testTimeZnmx、isEndZnmx
  5. 点击卡片，会跳转至对应的学习模块界面；
  6. 卡片中，智能记忆、智能听写、智能默写三种学习模块，分别用三种颜色区分；
  7. 学习个数使用数字展示，并且也使用进度条展示，学习个数/单元总数作为进度条百分比；


#### todo02
背景：
1. 已实现了单词训练界面，位于 src\views\dcyx\work-main\word-training.vue
	提供选择单元，点击智能记忆进入智能记忆界面的功能；
2. 已实现了智能记忆界面，位于 src\views\dcyx\learn-main\inteli-memory.vue
	提供基于本地模拟单词的，学习逻辑实现；
3. 已实现了 API ：
	/api/client/v1/word/page?unitId=1  单词列表查询
	返回示例：
	{
		"list": [
			{
				"id": "1",
				"spelling": "Hello",
				"syllable": "hel-lo",
				"meaningZhCn": "你好",
				"exampleEnUs": "Hello, how are you?",
				"exampleZhCn": "你好，你怎么样？",
				"unitName": "Unit 1: Greetings",
				"programId": "1",
				"seriesId": "1",
				"unitId": "1",
				"status": 1
			},],
			"total": 10
	}
4. 已实现了 API ：
	用户对单词的学习状态记录：
	1. 新增、/api/client/v1/user-word-status [post]，输入参数：
		{
			"id": 0,
			"userId": 0,
			"wordId": 0,
			"unitName": "",
			"programId": 0,
			"seriesId": 0,
			"learned": 0,
			"studyCount": 0,
			"correctCount": 0,
			"errorCount": 0,
			"lastStudyAt": ""
		}
	2. 修改 /api/client/v1/user-word-status/{id} [put]，
	输入参数：
	{
		"id": 0,
		"userId": 0,
		"wordId": 0,
		"unitName": "",
		"programId": 0,
		"seriesId": 0,
		"learned": 0,
		"studyCount": 0,
		"correctCount": 0,
		"errorCount": 0,
		"lastStudyAt": ""
	}
	3. 分页查询 /api/client/v1/user-word-status/page [get]，支持参数，pageNum、pageSize、wordId
	返回示例：
	"list": [
			{
				"id": 0,
				"userId": 0,
				"wordId": 0,
				"unitName": "",
				"programId": 0,
				"seriesId": 0,
				"learned": 0,
				"studyCount": 0,
				"correctCount": 0,
				"errorCount": 0,
				"lastStudyAt": ""
			}
		],
		"total": 0
5. 已实现API:
	新增用户单元学习状态 /api/client/v1/unit-study-status [POST]
	{
		"id": 0,
		"userId": 0,
		"unitId": 0,
		"memoryCompleted": 0,
		"memoryScore": 0,
		"memoryTimeConsuming": 0,
		"dictationCompleted": 0,
		"dictationScore": 0,
		"dictationTimeConsuming": 0,
		"writingCompleted": 0,
		"writingScore": 0,
		"writingTimeConsuming": 0,
		"overallCompleted": 0
	}
	查询用户单元学习状态分页列表 /api/client/v1/unit-study-status/page [get]，支持 unitId
	返回示例：
	 {
		"list": [
			{
				"id": 0,
				"userId": 0,
				"unitId": 0,
				"memoryCompleted": 0,
				"memoryScore": 0,
				"memoryTimeConsuming": 0,
				"dictationCompleted": 0,
				"dictationScore": 0,
				"dictationTimeConsuming": 0,
				"writingCompleted": 0,
				"writingScore": 0,
				"writingTimeConsuming": 0,
				"overallCompleted": 0
			}
		],
		"total": 0
	},
6. 以实现API：
	新增用户学习记录
	/api/client/v1/user-study-record [post] 传入参数
	{
		"id": 0,
		"userId": 0,
		"seriesId": 0,
		"programId": 0,
		"wordId": 0,
		"unitId": 0,
		"unitName": "",
		"status": 0,
		"source": "",
		"studyId": 0,
		"sourceFrom": "",
		"spellType": 0,
		"isTestWord": 0,
		"createUser": 0,
		"updateUser": 0
	}
	修改用户学习记录 /api/client/v1/user-study-record/{id} [put]
	传入参数：
	{
		"id": 0,
		"userId": 0,
		"seriesId": 0,
		"programId": 0,
		"wordId": 0,
		"unitId": 0,
		"unitName": "",
		"status": 0,
		"source": "",
		"studyId": 0,
		"sourceFrom": "",
		"spellType": 0,
		"isTestWord": 0,
		"createUser": 0,
		"updateUser": 0
	}
7. 尽可能参考本项目的实现风格；
8. 实现完整的 API、STORE、VIEW；

需求：
1. 在单词训练界面，点击智能记忆卡片后，
2. 进入智能记忆界面，实现基于 API 的单词列表数据请求；
3. 然后针对用户对单词的点击逻辑，保存用户对单词的学习记录；点击不认识时，记录 user-word-status （）、 user-study-record（ status 字段表示是否学会） ；
4. 在学习完成后，保存对单元-智能记忆的学习状态；

#### todo03
背景：
1. 播放单词的 API 已经实现： /api/client/v1/audio/{word} [get] ，返回音频文件 word.mp3；
2. 智能记忆通过本地文件方式，播放音频声音，文件位于 src\views\dcyx\learn-main\inteli-memory.vue, 代码段如下
 // 播放单词发音
const audioUrl = '/mp3s/good.mp3'
const playWordSound = () => {
  if ((window as any).wordAudio) {
    ;(window as any).wordAudio.pause()
    ;(window as any).wordAudio.currentTime = 0
  }
  ;(window as any).wordAudio = new window.Audio(audioUrl)
  ;((window as any).wordAudio as HTMLAudioElement).play()
}

需求：
1. 使用播放单词的 API，替换本地文件播放音频声音；
2. 尽可能保留原有功能；
3.  尽可能参考本项目的实现风格； 实现完整的 API、STORE、VIEW；

##### todo04
你已经实现了todo02、todo03，将它们作为背景信息；
实现下面需求，
1. 智能记忆通过本地文件方式，saveUserWordStatus 保存单词学习状态时， lastStudyAt 给的值是 2025-06-28T12:51:37.708Z， 它会出错，请给这种类别的值 2024-06-10 16:00:00；
2. 使用 interface ，在 API 文件中定义好用的 对象的结构；


#### todo05

背景：
1. user-word-status/update 新增OR更新用户单词学习状态  post
	传入 {
  "wordId": 1,
  "learned": 1
}
2. user-word-status/page 用户单词学习状态分页列表 get
	查出
	{
		"list": [
			{
				"id": "1",
				"userId": "1",
				"wordId": "1",
				"unitName": "Unit 1: Greetings",
				"programId": "1",
				"seriesId": "1",
				"learned": 1,
				"studyCount": 11,
				"correctCount": 10,
				"errorCount": 1,
				"lastStudyAt": "2025-06-28 22:02:55"
			},],
		"total": 8
	},
3. user-study-record/update 新增OR修改用户学习记录 post
传入
	{
  "wordId": 1,
  "status": 1,
  "source": "znjy",
  "sourceFrom": "znjy"
}
4. user-study-record/page 用户学习记录分页查询 get
查出
 {
		"list": [
			{
				"id": "1",
				"userId": "1",
				"seriesId": "1",
				"programId": "1",
				"wordId": "1",
				"unitId": "1",
				"unitName": "Unit 1: Greetings",
				"status": 1,
				"source": "app",
				"studyId": "10001",
				"sourceFrom": "mobile_app",
				"spellType": 2,
				"isTestWord": 0,
				"createUser": "1",
				"updateUser": "1"
			},],
		"total": 4
	},
5. src\views\dcyx\learn-main\inteli-memory.vue
6. src\api\dcyx\memory.api.ts
需求：
1. 修改 src\views\dcyx\learn-main\inteli-memory.vue ，以及对应的 store、API；
2. 使用上面的API实现每个单词的状态和记录的查询和更新；
3. 尽可能保留原有功能；
4. 尽可能参考本项目的实现风格； 实现完整的 API、STORE、VIEW；
	1. 函数参数和返回值，要指定数据结构类型，它们都应定义在 API 文件里，通过 interface 关键字；



#### todo06
需求：
1. src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面在输入框检测到输入后，自动隐藏单词拼写；

#### todo07
需求：
1. src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面，有效时长在认识单词阶段和听写单词阶段都显示；
2. 要求，每过 60 秒更新调用一次更新有效时长的 API， /api/client/v1/daily-time-records/add-online-seconds [post] 传入 {
		"effectiveSeconds": 60
}；
3. 要求如果 30 秒内，没有检测到鼠标或者键盘的输入，则提示用户长时间没操作，并暂停有效时长的更新；

#### todo08
问题：
1. 进入智能记忆界面时，有效时间应该开始计时，但是现在没有计时开始，src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面.
需求：
2. 请优化性能和稳定性；
3. 只有 30s 时间长度，没有检测到鼠标或者键盘输入时，提示用户长时间没操作，并暂停有效时长的更新；
4. 用户有鼠标或者键盘输入时，重置30s，并恢复有效时长的更新；


#### todo09
问题：
1. src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面 effectiveSeconds 变量的处理应该如下：
	1. 初始时，查询今日的有效时间；/api/client/v1/daily-time-records/today-online-seconds [get]
	返回数据如下：
		"data": {
		"id": "1",
		"userId": "1",
		"recordDate": "2025-06-29 00:00:00",
		"onlineTime": "2025-06-29 00:00:00",
		"effectiveTime": "2025-06-29 00:00:00",
		"efficiency": "0%",
		"onlineSeconds": 0,
		"effectiveSeconds": 0
	},
	2.  effectiveSeconds 值从 data.effectiveSeconds 获取；
	3. 每过 60 秒更新调用一次更新有效时长的 API， /api/client/v1/daily-time-records/add-online-seconds [post] 传入 {
		"effectiveSeconds": 60
	}；
	4. 每秒钟 effectiveSeconds 的值要加 1s；
	5. 要求如果 30 秒内，没有检测到鼠标或者键盘的输入，则提示用户长时间没操作，并暂停有效时长的递增和 API 更新；
	6. 用户有鼠标或者键盘输入时，重置30s，并恢复有效时长的递增和 API 更新；
	7. 从暂停的有效时长再次开始计算，
	8. effectiveSeconds 在第一次进入时，才从 API 获取；调用更新 effectiveSeconds 的API后，effectiveSeconds 的值要更新；
	9. 在暂停取消后，不要查询 effectiveSeconds API 和更新 effectiveSeconds 的值；要从上一次的计时基础上，往后持续更新；

#### todo10
需求：
1. src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面
2. 有效时长功能描述：
	1. 初始时，查询今日的有效时间；/api/client/v1/daily-time-records/today-online-seconds [get]，更新有效时长；
	2. 学习时，每秒递增有效时长；
	3. 暂停时，保存暂停时的有效时长；
	4. 暂停取消后，从暂停时的有效时长基础上，继续递增；
	5. 有效时长每递增 60 s，调用一次更新有效时长的 API， /api/client/v1/daily-time-records/add-online-seconds [post] 传入 {
		"effectiveSeconds": 60
	}；


#### todo11
需求：
1. src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面
2. 解决F5刷新， 进入该界面时，单词列表为空的问题；

#### todo12
需求：
1. src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面
2. 不用频繁的查询单词学习状态；
3. 单词状态更新时机如下：
	1. 在倒计时结束时，更新单词状态为不认识；
	2. 在点击确认认识，或者点击确认不认识时，更新单词的状态为对应的认识或者不认识；

#### todo13
需求：
1. src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面
2. 底部信息栏：
生词数: 1
熟词数: 1
总词数: 10
当前进度: 5/10
3. 不认识单词时，生词数增加；
4. 认识单词时，熟词数增加；
5. 生词数+熟词数=已学习单词数；
6. 当前进度：已学习单词数/总单词数；

#### todo14
需求：
1. src\views\dcyx\learn-main\inteli-memory.vue 智能记忆界面
2. 单词记忆阶段完成后，进入单词听写阶段前，需要更新该单元对应的单词记忆阶段的学习状态和学习信息；
	1. 使用更新 API /api/client/v1/unit-study-stats/update [post] 传入参数：
	studyNumZnjy 表示单词记忆的熟词个数、restudyNumZnjy 表示单词记忆的生词个数， 两者合应该等于 unitTotal ，单元的单词总数；
	studyNumZnmx 表示单词听写的熟词个数、restudyNumZnmx 表示单词听写的生词个数， 两者合应该等于 unitTotal ，单元的单词总数；
	studyNumZntx 表示单词默写的熟词个数、restudyNumZntx 表示单词默写的生词个数；
	isEndZnjy 表示是否完成了单词记忆阶段的学习；
	isEndZnmx 表示是否完成了单词听写阶段的学习；
	isEndZntx 表示是否完成了单词默写阶段的学习；
	testTimeZnjy表示单词记忆阶段的学习时长；
	testTimeZnmx表示单词听写阶段的学习时长；
	testTimeZntx表示单词默写阶段的学习时长；
		{
			"userId": 0,
			"unitId": 0,
			"unitTotal": 0,
			"studyNumZnjy": 0,
			"studyNumZnmx": 0,
			"studyNumZntx": 0,
			"testTimeZnjy": 0,
			"testTimeZnmx": 0,
			"testTimeZntx": 0,
			"restudyNumZnjy": 0,
			"restudyNumZnmx": 0,
			"restudyNumZntx": 0,
			"isEndZnjy": 0,
			"isEndZnmx": 0,
			"isEndZntx": 0
		}
		请实现该 API 的调用；在 API 层，并定义清晰的结构，在API层，使用 interface 关键字；
	2. 实现完整的 API、STORE、VIEW；
	3. 尽可能保留原有功能；
	4. 尽可能参考本项目的实现风格； 实现完整的 API、STORE、VIEW；
	5. 函数参数和返回值，要指定数据结构类型，它们都应定义在 API 文件里，通过 interface 关键字；



#### todo15
1. 单词学习主控界面，src\views\dcyx\work-main\word-training.vue
2. 查询了 fetchUnitInfo 单元统计信息；
3. currentUnitStudyStats 存在当前单元的统计信息；
4. 在主控界面，如果当前单元的记忆模块已经完成了学习，通过  isEndZnjy 字段表示，则显示在卡片上显示已完成；并且不支持点击进入智能学习界面；
5. 需要点击顶部栏的重学按钮，点击重置按钮，弹出一个对话框；
	1. 支持勾多选已经完成的模块，点击重置，则对将这些模块的统计信息重置；
6. 实现完整的 API、STORE、VIEW；
7. 尽可能保留原有功能；
<!-- 8. 尽可能参考本项目的实现风格； 实现完整的 API、STORE、VIEW； -->
9. 函数参数和返回值，要指定数据结构类型，它们都应定义在 API 文件里，通过 interface 关键字；

你可以参考 todo14 作为背景信息；

#### todo16
1. 单词学习主控界面，src\views\dcyx\work-main\word-training.vue
2. 查询了 fetchUnitInfo 单元统计信息；返回对象示例为 const currentUnitStudyStats = ref<UnitStudyStatsItem>()
export interface UnitStudyStatsItem {
  id: string
  userId: string
  unitId: string
  unitTotal: number
  studyNumZnjy: number
  studyNumZnmx: number
  studyNumZntx: number
  testTimeZnjy: number
  testTimeZnmx: number
  testTimeZntx: number
  restudyNumZnjy: number
  restudyNumZnmx: number
  restudyNumZntx: number
  isEndZnjy: number
  isEndZnmx: number
  isEndZntx: number
}
3. 根据信息，
	isEndZnjy: number
  isEndZnmx: number
  isEndZntx: number
	切换三个模式卡片的状态，分为两种，
	isEndZnjy 为 0， 智能记忆卡片状态为开始学习；为1，已完成学习；
	isEndZnmx 为 0， 单词听写卡片状态为开始学习；为1，已完成学习；
	isEndZntx 为 0， 单词默写卡片状态为开始学习；为1，已完成学习；
4. 开始学习状态，点击会进入对应的智能学习界面；
5. 已完成学习状态，会显示已学习单词数，熟词数，生词数，当前进度，学习时间长度；
6. 请在合适的位置提供一个重学按钮，点击后，会弹出一个对话框，支持勾选已经完成的模块，点击重置，则对将这些模块的统计信息重置；
7. 重置可以使用 API 实现，/api/client/v1/unit-study-stats/update [post] 传入参数：
		{
			"unitId": 0,
			"unitTotal": wordList.length,
			"studyNumZnjy": 0,
			"testTimeZnjy": 0,
			"studyNumZnmx": 0,
			"testTimeZnmx": 0,
			"studyNumZntx": 0,
			"testTimeZntx": 0,
			"isEndZnjy": 0,
			"isEndZnmx": 0,
			"isEndZntx": 0
		}
	8. 实现完整的 API、STORE、VIEW；


#### todo17
1. 单词学习主控界面，src\views\dcyx\work-main\word-training.vue
2. 进入 http://localhost:3000/#/work/word-training 页面时，没有加载出三个学习模块；

#### todo18
1. 单词学习主控界面，src\views\dcyx\work-main\word-training.vue
2. 如果查询了 fetchUnitInfo 单元三个模块的学习统计数据，返回信息没有，，则卡片不会出现；
3. 但是，更合适的逻辑应该是：
	1. 如果没有统计数据，三种模块的学习卡片也要出现，显示默认的信息，单词总数，开始学习等等；
	3. 单词总数，可以通过 unit store 的 unitList 找到 unitId 对应的项，


#### todo19
1. 请你重新实现一个系统主页；src\views\dcyx\sys-main\index.vue 及其 src\views\dcyx\sys-main\compoments
2. 风格参考 src\views\dcyx\work-main 目录下的页面实现；