import request from '@/utils/request'

const UNIT_BASE_URL = '/api/client/v1/unit'
const USER_CURRENT_UNIT_BASE_URL = '/api/client/v1/user-current-unit'
const UNIT_STUDY_STATUS_BASE_URL = '/api/client/v1/unit-study-status'
const UNIT_STUDY_STATS_BASE_URL = '/api/client/v1/unit-study-stats'

const DCYX_UNITS_API = {
  // 查询用户选中当前单元的接口
  getCurrentUnit: () => {
    return request<any, UserCurrentUnit>({
      url: `${USER_CURRENT_UNIT_BASE_URL}/current`,
      method: 'get',
    })
  },
  // 更新用户选中当前单元的接口
  updateCurrentUnit: (data: UpdateUserCurrentUnitPayload) => {
    return request<UpdateUserCurrentUnitPayload, null>({
      url: `${USER_CURRENT_UNIT_BASE_URL}/current`,
      method: 'post',
      data,
    })
  },
  // 查询单元
  getUnitsByProgramId: (params: UnitQueryParams) => {
    return request<UnitQueryParams, PageResult<UnitItem[]>>({
      url: `${UNIT_BASE_URL}/page`,
      method: 'get',
      params,
    })
  },
  // 查询单元学习状态
  getUnitStudyStatus: (params: UnitStudyStatusQueryParams) => {
    return request<
      UnitStudyStatusQueryParams,
      PageResult<UnitStudyStatusItem[]>
    >({
      url: `${UNIT_STUDY_STATUS_BASE_URL}/page`,
      method: 'get',
      params,
    })
  },
  // 查询单元学习统计
  getUnitStudyStats: (params: UnitStudyStatsQueryParams) => {
    return request<UnitStudyStatsQueryParams, PageResult<UnitStudyStatsItem[]>>(
      {
        url: `${UNIT_STUDY_STATS_BASE_URL}/page`,
        method: 'get',
        params,
      },
    )
  },
  // 更新单元状态
  updateUnitStudyStatus: (data: UpdateStudyUnitStatusPayload) => {
    return request<UpdateStudyUnitStatusPayload, any>({
      url: `${UNIT_STUDY_STATUS_BASE_URL}/update`,
      method: 'post',
      data,
    })
  },
  // 更新单元学习统计
  updateUnitStudyStats: (data: UpdateUnitStudyStatsPayload) => {
    return request<UpdateUnitStudyStatsPayload, any>({
      url: `${UNIT_STUDY_STATS_BASE_URL}/update`,
      method: 'post',
      data,
    })
  },
}

export default DCYX_UNITS_API

/**
 * 				"id": 0,
				"seriesId": 0,
				"programId": 0,
				"programName": "",
				"name": "",
				"nameEnUs": "",
				"nameZhCn": "",
				"nameZhBig": "",
				"unitIndex": 0,
				"createUser": 0,
				"updateUser": 0,
				"status": 0,
				"totalNum": 0
 */
export interface UnitItem {
  id: number
  seriesId: number
  programId: number
  programName: string
  name: string
  nameEnUs: string
  nameZhCn: string
  nameZhBig: string
  unitIndex: number
  createUser: number
  updateUser: number
  status: number
  totalNum: number
}

export interface UnitStudyStatusQueryParams {
  unitId: number
  userId: number
}

export interface UnitStudyStatsQueryParams {
  unitId: number
  userId: number
}

/**
 * 			{
				"id": "1",
				"userId": "1",
				"unitId": "1",
				"unitTotal": 12,
				"studyNumZnjy": 8,
				"studyNumZnmx": 5,
				"studyNumZntx": 7,
				"testTimeZnjy": 3,
				"testTimeZnmx": 2,
				"testTimeZntx": 4,
				"restudyNumZnjy": 2,
				"restudyNumZnmx": 1,
				"restudyNumZntx": 3,
				"isEndZnjy": 1,
				"isEndZnmx": 0,
				"isEndZntx": 1
			}
 */
export interface UnitStudyStatsItem {
  id: number
  userId: number
  unitId: number
  unitTotal: number
  studyNumZnjy: number
  studyNumZnmx: number
  studyNumZntx: number
  testTimeZnjy: number
  testTimeZnmx: number
  testTimeZntx: number
  restudyNumZnjy: number
  restudyNumZnmx: number
  restudyNumZntx: number
  isEndZnjy: number
  isEndZnmx: number
  isEndZntx: number
}

/**
			{
				"id": "1",
				"userId": "1",
				"unitId": "1",
				"memoryCompleted": 1,
				"memoryScore": 85,
				"memoryTimeConsuming": 120,
				"dictationCompleted": 1,
				"dictationScore": 90,
				"dictationTimeConsuming": 150,
				"writingCompleted": 1,
				"writingScore": 66,
				"writingTimeConsuming": 6000,
			},
 */
export interface UnitStudyStatusItem {
  id: number
  userId: number
  unitId: number
  memoryCompleted: number
  memoryScore: number
  memoryTimeConsuming: number
  dictationCompleted: number
  dictationScore: number
  dictationTimeConsuming: number
  writingCompleted: number
  writingScore: number
  writingTimeConsuming: number
}

export interface UnitQueryParams extends PageQuery {
  programId: number
}

export interface UnitCountWordItem {
  unitTotal: number
  studyNumForZnjy: number
  studyNumForZnmx: number
  studyNumForZntx: number
  testTimeForZnjy: number
  testTimeForZnmx: number
  testTimeForZntx: number
  restudyNumForZnjy: number
  restudyNumForZnmx: number
  restudyNumForZntx: number
  isEndZnjy: number
  isEndZntx: number
  isEndZnmx: number
}

export interface UserCurrentUnit {
  id: number
  userId: number
  username: string
  seriesId: number
  semesterId: number
  programId: number
  wordId: number
  wordIdx: number
  groupId: string
  fortify: string
  unitId: number
  unitName: string
  source: string
  errors: number
  rights: number
  isEnd: number
  errorWordInfo: string
}

export interface UpdateUserCurrentUnitPayload {
  // id: number
  userId: number
  // username: string
  // seriesId: number
  // programId: number
  // wordId: number
  // wordIdx: number
  // groupId: string
  // fortify: string
  unitId: number
  // unitName: string
  // source: string
  // errors: number
  // rights: number
  // isEnd: number
  // errorWordInfo: string
}

export interface UpdateUnitStudyStatsPayload {
  unitId: number
  unitTotal: number
  studyNumZnmx: number
  testTimeZnmx: number
  restudyNumZnmx: number
  isEndZnmx: number
}

/**
 * {
  "unitId": 1,
  "source": 2,  0-记忆、1-听写、2-默写
  "memoryCompleted": 1,
  "memoryScore": 33,
  "memoryTimeConsuming": 3000, 测试用时，单位秒
  "dictationCompleted": 1,
  "dictationScore": 44,
  "dictationTimeConsuming": 4000,
  "writingCompleted": 1,
  "writingScore": 66,
  "writingTimeConsuming": 6000
  }
 */
export interface UpdateStudyUnitStatusPayload {
  unitId: number
  source: number
  writingCompleted: number
  writingScore: number
  writingTimeConsuming: number
}
