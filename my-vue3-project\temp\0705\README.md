#### todo01
1. 用户退出后， 清空 store 模块的相关信息，currentProgram 和 currentUnit；
2. src\views\dcyx\sys-main\index.vue 存在用户退出的函数调用；
3. src\store\modules\dcyx_program.store.ts、src\store\modules\dcyx_unit.store.ts 存在 store 的初始化函数和状态保存；
4. 需要在用户退出后，执行store 的反初始化函数。


#### todo02
1. 当前教材模块信息，总是加载不不出来，位于 src\views\dcyx\sys-main\compoments\DcyxSysRightBoard\index.vue
2. dcyxProgramStore.currentProgram 信息为空，
3. 按 F5 刷新再能显示出信息；
解决问题

#### todo03
1. 请帮我重点检查和优化代码；
2. 在 src\views\dcyx\sys-main 目录下的 view 视图文件；
3. 它们依赖导入的其他模块文件，你可以适当检查和优化；

#### todo04
背景：
1. 已经实现了 store、view；
2. store：
  1. 教材：src\store\modules\dcyx_program.store.ts
  2. 单元：src\store\modules\dcyx_unit.store.ts
  3. 系列：src\store\modules\dcyx_series.store.ts
  4. 用户：src\store\modules\dcyx_user.store.ts
3. view：
  1. 主页右面版：src\views\dcyx\sys-main\compoments\DcyxSysRightBoard\index.vue
  2. 主页左面板：src\views\dcyx\sys-main\compoments\DcyxSysLeftBoard\index.vue
  3. 主页顶部栏：src\views\dcyx\sys-main\compoments\DcyxNavBar\index.vue
  4. 主页：src\views\dcyx\sys-main\index.vue
  5. 登录页：src\views\login\index.vue
  6. 选择教材模块页：src\views\dcyx\sys-main\compoments\DcyxSelectStudyModule
已实现需求：
1. 改造 store 和 view 代码实现；
2. 对 store 提出要求：
  1. 针对使用了 API 的函数，都要使用 new Promise 包裹，并且使用 resolve 返回；不使用 try catch；
  2. 每个函数内只允许调用一次 API，职责明确；
3. 对 view 提出要求：
  1. 针对使用了 store 的访问数据的函数，都使用 try catch 包裹；
新需求：
1. 针对 view：
  1. 将 store 的数据都保存到 view 组件的内部变量中，并且确保从 store 中获取的变量的，能够真的获取的到值；
  2. 将 store 的初始化函数，都放在 view 组件的 onMounted 中；
  3. 将 store 的反初始化函数，都放在 view 组件的 onBeforeUnmount 中；
2. 针对 store：
  1. 编写合适的初始化和反初始化函数；
    1. 请实现完整的初始化函数；
    2. 请实现完整的反初始化函数；
  2. 初始化函数确保 store 中的变量值，都能够稳定加载成功；
  3. 反初始化函数确保 store 中的变量值，都能够稳定清空；
4. 代码简单明了，稳定性强，结构清晰；
5. 每个函数实现功能明确；
6. 以你的经验进行代码优化；


#### todo05
1. 为啥 inited.value 的值，总是 true？通过调试打印该值总为 true；明明初始化为 false；
  1. 教材：src\store\modules\dcyx_program.store.ts
  2. 单元：src\store\modules\dcyx_unit.store.ts
  3. 系列：src\store\modules\dcyx_series.store.ts
  4. 用户：src\store\modules\dcyx_user.store.ts
3. 针对 store：
  1. 编写合适的初始化和反初始化函数；
    1. 请实现完整的初始化函数；包括但不限于 init、currentUnit、unitsList、currentProgram、semesterList、serieList 等等变量；
      每个 store 文件的初始化和反初始化，应该涉及其通过 API 获取的变量；
    2. 请实现完整的反初始化函数；

#### todo06
1. 智能记忆功能界面， src\views\dcyx\learn-main\inteli-memory.vue
2. 单词听写状态时，修正播放的单词读音为正在听写的单词；(已完成)
3. 单词认识状态时，最后一个单词认知结束时，进入单词听写状态，此时播放的还是最后一个单词的读音，理应播放第一个单词听写的读音；（已完成）
4. 单词听写状态时，每次点击回车键，切换下一个听写单词时，答案会出现。理应切换单词时，答案隐藏，直到输入单词后，输入回车，检查错误时，答案出现；再次输入单词时，答案会隐藏；
  1.

#### todo07
1. 智能记忆功能界面， src\views\dcyx\learn-main\inteli-memory.vue
2. 单词听写状态时，每次单词都会显示出来，这是问题，理应单词拼写默认是不显示的，检查拼写错误后，才会显示单词拼写。
3. 听写阶段，单词拼写显示的规则过于复杂，请设计简明容易理解的逻辑， 下面是判断是否显示拼写：
<div
                    v-if="
                      dictationShowSpelling &&
                      (dictationShowAnswer || dictationReadyForNext)
                    "
                    style="text-align: center; margin-bottom: 16px"
                  >
                    <template v-if="dictationShowAnswer && dictationError">
                      <span

#### todo08
1. 智能记忆功能界面， src\views\dcyx\learn-main\inteli-memory.vue
2. nextWord 切换下一个单词时，判断当前阶段
  1. 如果是单词认识阶段
  2. 如果是单词听写阶段，需要将 dictationError 置为 false；

#### todo09
1. 单词学习主页面：src\views\dcyx\work-main\word-training.vue
1. 优化重置学习模块代码实现；// 重置学习模块
const handleReset = async () => {
2. 使其清晰明了，结构明确；