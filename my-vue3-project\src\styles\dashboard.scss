// Dashboard specific styles
.dashboard {
  &-container {
    padding: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(135deg, #e0eafc 0%, #cfdef3 100%);
    font-family: var(--el-font-family);
    color: var(--el-text-color-primary);
  }

  &-header {
    width: 100%;
    background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
    border-radius: 0 0 18px 18px;
    margin-bottom: 0;
  }

  &-main {
    display: flex;
    flex-direction: row;
    height: calc(100vh - 80px);
    background: linear-gradient(90deg, #cee1f4 0%, #8393a0 100%) !important;
    padding: 12px;
    gap: 12px;
    box-sizing: border-box;
  }

  &-left {
    min-width: 300px;
    max-width: 340px;
    background: none;
  }

  &-right {
    flex: 1;
    background: none;
  }

  &-content {
    flex: 1;
    padding-left: 32px;
  }
}

// Dashboard card styles
.dashboard-card {
  background: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%) !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06) !important;
  height: calc(100vh - 104px) !important;
  overflow-y: auto;
}

// Dashboard typography
.dashboard {
  &-title {
    color: var(--el-color-primary) !important;
    font-weight: bold !important;
    font-size: 16px !important;
    margin-bottom: 8px !important;
  }

  &-text {
    color: var(--el-text-color-primary) !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  &-highlight {
    color: var(--el-color-warning) !important;
  }

  &-success {
    color: var(--el-color-success) !important;
  }
}

// Custom Element Plus overrides for dashboard
.el-message-box {
  :deep() {
    border-radius: 12px !important;
    padding: 20px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  }

  :deep(.el-message-box__header) {
    padding: 0 0 16px 0 !important;
  }

  :deep(.el-message-box__title) {
    color: var(--el-color-primary) !important;
    font-size: 18px !important;
    font-weight: 600 !important;
  }

  :deep(.el-message-box__content) {
    padding: 16px 0 !important;
    color: var(--el-text-color-regular) !important;
    font-size: 14px !important;
  }

  :deep(.el-message-box__btns) {
    padding: 16px 0 0 0 !important;
    justify-content: flex-end !important;
    gap: 12px !important;

    .el-button {
      border-radius: 6px !important;
      padding: 8px 20px !important;
      font-weight: 500 !important;
      transition: all 0.3s ease !important;

      &--default {
        border-color: var(--el-border-color) !important;
        &:hover {
          border-color: var(--el-color-primary) !important;
          color: var(--el-color-primary) !important;
        }
      }

      &--primary {
        background: var(--el-color-primary) !important;
        border-color: var(--el-color-primary) !important;
        &:hover {
          opacity: 0.9 !important;
          transform: translateY(-1px) !important;
        }
      }
    }
  }
}
