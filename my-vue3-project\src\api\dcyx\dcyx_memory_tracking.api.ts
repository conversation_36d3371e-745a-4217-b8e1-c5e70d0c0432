import request from '@/utils/request'

export interface ReviewWord {
  id: string
  spelling: string
  syllable: string
  meaningZhCn: string
  exampleEnUs: string
  exampleZhCn: string
  unitName: string
  programId: string
  seriesId: string
  unitId: string
  status: number
}

export interface ReviewWordsParams {
  programId: number
  source: 'znjy' | 'zntx' | 'znmx'
  unitId?: number
}

export interface ReviewWordsResponse {
  code: string
  data: ReviewWord[]
  msg: string
}

/**
 * 获取复习单词列表
 */
export const getReviewWords = (params: ReviewWordsParams) => {
  return request<ReviewWord[]>({
    url: '/api/client/v1/user-study-record/review-words',
    method: 'get',
    params,
  })
}
