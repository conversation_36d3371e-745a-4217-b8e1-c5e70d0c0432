import request from '@/utils/request'

const DAILY_TIME_RECORDS_BASE_URL = '/api/client/v1/daily-time-records'

const DAILY_TIME_RECORDS_API = {
  getTodayOnlineSeconds: () => {
    return request<any, DailyTimeRecord>({
      url: `${DAILY_TIME_RECORDS_BASE_URL}/today-online-seconds`,
      method: 'get',
    })
  },
  addOnlineOrEffectiveSeconds: (data: AddOnlineOrEffectiveSeconds) => {
    return request<any, DailyTimeRecord>({
      url: `${DAILY_TIME_RECORDS_BASE_URL}/add-online-seconds`,
      method: 'post',
      data: data,
    })
  },
}
// export function getTodayOnlineSeconds() {
//   return request<any, DailyTimeRecord>({
//     url: `${DAILY_TIME_RECORDS_BASE_URL}/today-online-seconds`,
//     method: 'get',
//   })
// }

// export function addOnlineSeconds(data: AddOnlineSecond) {
//   return request<any, any>({
//     url: `${DAILY_TIME_RECORDS_BASE_URL}/add-online-seconds`,
//     method: 'post',
//     data: data,
//   })
// }

export default DAILY_TIME_RECORDS_API

/**
 * {
    "id": "11",
    "userId": "6",
    "recordDate": "2025-06-21 00:00:00",
    "time1": "2025-06-21 00:00:00",
    "time2": "2025-06-21 00:00:00",
    "efficiency": "0%",
    "seconds": 0
}
 */
export interface DailyTimeRecord {
  id: string
  onlineTime: string // 在线时长（秒）
  effectiveTime: string
  efficiency: string
  userId: string
  recordDate: string
  effectiveSeconds: number
  onlineSeconds: number
}

export interface AddOnlineOrEffectiveSeconds {
  effectiveSeconds: number
  onlineSeconds: number
}
