<template>
  <div class="word-training-outer">
    <el-row justify="center" align="middle" style="height: 100vh">
      <el-col :span="32">
        <el-card class="main-card">
          <div class="word-training">
            <!-- 顶部导航栏 -->
            <div class="word-training__header">
              <div class="header-left">
                <h2 class="word-training__title">智能默写</h2>
                <span class="unit-name">{{ currentUnit?.unitName }}</span>
              </div>
              <el-button @click="handleExit" type="primary" plain>
                <el-icon><Back /></el-icon>
                退出
              </el-button>
            </div>

            <!-- 默写内容 -->
            <div class="word-training__content">
              <div class="word-card">
                <!-- 进度条 -->
                <div class="word-card__progress">
                  <el-progress
                    :percentage="writingProgress"
                    :format="progressFormat"
                    status="success"
                  />
                  <span class="progress-text">
                    {{
                      currentPhase === 'writing'
                        ? `${writingCompletedCount}/${totalWords}`
                        : `${writingReviewCompletedCount}/${writingReviewWords.length}`
                    }}
                  </span>
                </div>

                <!-- 第一行：词性、词义 -->
                <div class="word-card__meaning" style="margin-bottom: 16px">
                  <div class="meaning-content">
                    <span class="part-of-speech"
                      >{{ currentWord?.meaningZhCn.split('.')[0] }}.</span
                    >
                    <span class="meaning">{{
                      currentWord?.meaningZhCn.split('.')[1]
                    }}</span>
                  </div>
                </div>

                <!-- 第二行：输入框 -->
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 16px;
                    margin-bottom: 16px;
                  "
                >
                  <el-input
                    ref="writingInputRef"
                    v-model="writingInput"
                    placeholder="请输入单词拼写"
                    @keyup.enter="handleWritingEnter"
                    :status="writingError ? 'error' : ''"
                    :disabled="writingReadyForNext"
                    style="width: 240px"
                    autofocus
                  />
                  <el-icon
                    v-if="
                      !writingError &&
                      writingInput &&
                      writingInput.trim().toLowerCase() ===
                        currentWord?.spelling.toLowerCase() &&
                      !writingReadyForNext
                    "
                    style="color: #67c23a; margin-left: 8px"
                  >
                    <Check />
                  </el-icon>
                  <el-icon
                    v-if="writingReadyForNext"
                    style="color: #409eff; margin-left: 8px; cursor: pointer"
                    @click="nextWritingWord"
                  >
                    <ArrowRight />
                  </el-icon>
                </div>

                <!-- 第三行：拼写错误时显示答案，拼写正确时显示音标和例句 -->
                <div
                  v-if="writingShowSpelling"
                  style="text-align: center; margin-bottom: 16px"
                >
                  <template v-if="writingError">
                    <span
                      v-for="(ch, idx) in currentWord?.spelling.split('')"
                      :key="idx"
                      :style="{
                        color: writingErrorMap[idx] ? '#f56c6c' : '#303133',
                        fontWeight: writingErrorMap[idx] ? 'bold' : 'normal',
                        fontSize: '22px',
                        marginRight: '2px',
                      }"
                    >
                      {{ ch }}
                    </span>
                  </template>
                  <template v-else>
                    <span style="font-size: 22px; color: #409eff">{{
                      currentWord?.syllable
                    }}</span>
                    <div class="word-card__example" style="margin-top: 16px">
                      <div class="example-en">
                        {{ currentWord?.exampleEnUs }}
                      </div>
                      <div class="example-cn">
                        {{ currentWord?.exampleZhCn }}
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>

            <!-- 底部进度信息 -->
            <div class="word-training__footer">
              <div class="progress-info">
                <span>有效时长: {{ formatSeconds(effectiveSeconds) }}</span>
                <span>总时长: {{ formatSeconds(totalStudyTime) }}</span>

                <!-- 默写学习阶段统计 -->
                <template v-if="currentPhase === 'writing'">
                  <span>默写完成单词数: {{ writingCompletedCount }}</span>
                  <span>需要默写的单词总个数: {{ totalWords }}</span>
                  <span>默写正确数: {{ writingCorrectCount }}</span>
                  <span>默写错误数: {{ writingErrorCount }}</span>
                  <span class="writing">默写学习阶段</span>
                </template>

                <!-- 默写复习阶段统计 -->
                <template v-if="currentPhase === 'writingReview'">
                  <span
                    >默写复习完成单词数: {{ writingReviewCompletedCount }}</span
                  >
                  <span
                    >需要默写复习的单词总个数:
                    {{ writingReviewWords.length }}</span
                  >
                  <span class="writing-review">默写复习阶段</span>
                </template>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, Check, ArrowRight } from '@element-plus/icons-vue'
import type { WordItem } from '@/api/dcyx/writing.api'
import { useDcyxWritingStore } from '@/store/modules/dcyx_writing.store'
import { useDcyxUnitStore } from '@/store/modules/dcyx_unit.store'
import DAILY_TIME_RECORDS_API from '@/api/dcyx/daily-tIme-records.api'

// 阶段类型定义
type WritingPhase = 'writing' | 'writingReview' | 'finished'

const router = useRouter()
const writingStore = useDcyxWritingStore()
const unitStore = useDcyxUnitStore()

// 阶段管理
const currentPhase = ref<WritingPhase>('writing')

// 单词列表
const wordList = ref<WordItem[]>([])

// 默写学习阶段相关
const writingIndex = ref(0)
const writingCorrectCount = ref(0)
const writingErrorCount = ref(0)
const writingInput = ref('')
const writingError = ref(false)
const writingErrorMap = ref<boolean[]>([])
const writingReadyForNext = ref(false)
const writingShowSpelling = ref(false)
const writingFirstAttempt = ref<Set<number>>(new Set())
const writingFailedWordIds = ref<Set<number>>(new Set())

// 默写复习阶段相关
const writingReviewWords = ref<WordItem[]>([])
const writingReviewIndex = ref(0)
const writingReviewCorrectCountRef = ref(0)
const writingReviewCompletedWordIds = ref<Set<number>>(new Set())
const writingReviewOnceAttempt = ref(true)

// 时间相关
const effectiveSeconds = ref(0)
const totalStudyTime = ref(0)
const secondTimer = ref<number>()
const totalStudyTimer = ref<number>()

// 有效时长相关
const idleTimer = ref<number>()
const isPaused = ref(false)
const idleLimit = 30 // 30秒无操作
const lastAction = ref(Date.now())
let pausedEffectiveSeconds = 0 // 暂停时的有效时长

// 监听用户操作
const activityEvents = ['mousemove', 'keydown', 'mousedown', 'touchstart']
const resetIdle = () => {
  lastAction.value = Date.now()
  if (isPaused.value) {
    isPaused.value = false
    // 恢复时从暂停时的有效时长继续递增
    effectiveSeconds.value = pausedEffectiveSeconds
    startSecondTimer()
    ElMessageBox.close()
  }
}

const handleIdle = () => {
  if (!isPaused.value) {
    isPaused.value = true
    // 暂停时保存当前有效时长
    pausedEffectiveSeconds = effectiveSeconds.value
    stopSecondTimer()
    ElMessageBox.alert('您已30秒无操作，已暂停计时', '提示', {
      showClose: false,
      closeOnClickModal: false,
      closeOnPressEscape: false,
    })
  }
}

const startIdleTimer = () => {
  if (idleTimer.value) clearInterval(idleTimer.value)
  idleTimer.value = window.setInterval(() => {
    if (Date.now() - lastAction.value > idleLimit * 1000) {
      handleIdle()
    }
  }, 1000)
}

const stopIdleTimer = () => {
  if (idleTimer.value) clearInterval(idleTimer.value)
}

const addActivityListeners = () => {
  activityEvents.forEach((evt) => window.addEventListener(evt, resetIdle))
}
const removeActivityListeners = () => {
  activityEvents.forEach((evt) => window.removeEventListener(evt, resetIdle))
}

// 计算属性
const currentWord = computed(() => {
  switch (currentPhase.value) {
    case 'writing':
      return wordList.value[writingIndex.value]
    case 'writingReview':
      return writingReviewWords.value[writingReviewIndex.value]
    default:
      return null
  }
})

const currentUnit = computed(() => unitStore.currentUnit)
const totalWords = computed(() => wordList.value.length)

// 默写学习阶段进度计算
const writingCompletedCount = computed(
  () => writingCorrectCount.value + writingErrorCount.value,
)

// 默写复习阶段进度计算
const writingReviewCompletedCount = computed(
  () => writingReviewCompletedWordIds.value.size,
)

const writingProgress = computed(() => {
  switch (currentPhase.value) {
    case 'writing':
      return Math.round((writingCompletedCount.value / totalWords.value) * 100)
    case 'writingReview':
      return Math.round(
        (writingReviewCompletedCount.value / writingReviewWords.value.length) *
          100,
      )
    default:
      return 0
  }
})

// 进度条格式化
const progressFormat = (percentage: number) => `${percentage}%`

// 处理退出
const handleExit = () => {
  router.push('/work/word-training')
}

// 默写阶段：输入框自动聚焦
const writingInputRef = ref<HTMLInputElement | null>(null)
const focusWritingInput = () => {
  nextTick(() => {
    writingInputRef.value?.focus()
  })
}

// 快捷键处理
const handleKeydown = (e: KeyboardEvent) => {
  if (
    currentPhase.value === 'writing' ||
    currentPhase.value === 'writingReview'
  ) {
    if (e.key === 'Enter') {
      handleWritingEnter()
    }
    return
  }
}

// 监听输入变化
watch(writingInput, (val) => {
  if (val) {
    writingShowSpelling.value = false
  }
})

// 默写阶段：处理输入
const handleWritingEnter = async () => {
  if (writingReadyForNext.value) {
    nextWritingWord()
    return
  }

  if (!writingInput.value || !currentWord.value) return

  const wordId = Number(currentWord.value.id)
  const isCorrect =
    writingInput.value.trim().toLowerCase() ===
    currentWord.value.spelling.toLowerCase()
  const isFirstAttempt = !writingFirstAttempt.value.has(wordId)
  const isOnceAttemp = writingReviewOnceAttempt.value

  if (isCorrect) {
    // 拼写正确
    writingError.value = false
    writingShowSpelling.value = true
    writingReadyForNext.value = true

    if (isFirstAttempt) {
      // 首次拼写正确
      if (currentPhase.value === 'writing') {
        writingCorrectCount.value++
        try {
          await writingStore.updateWritingRecord({
            wordId,
            status: 1,
            source: 'znmx',
            sourceFrom: 'znmx',
          })
        } catch {
          ElMessage.error('保存记录失败')
        }
      }
    }

    // 一次拼写正确
    if (isOnceAttemp) {
      if (currentPhase.value === 'writingReview') {
        // 在默写复习阶段，我们需要手动跟踪正确的数量
        writingReviewCorrectCountRef.value++
        writingReviewCompletedWordIds.value.add(wordId)
      }
    }
  } else {
    // 拼写错误
    writingError.value = true
    writingShowSpelling.value = true
    writingFirstAttempt.value.add(wordId)

    if (isFirstAttempt) {
      // 首次拼写错误
      if (currentPhase.value === 'writing') {
        writingErrorCount.value++
        writingFailedWordIds.value.add(wordId)
        try {
          await writingStore.updateWritingRecord({
            wordId,
            status: 0,
            source: 'znmx',
            sourceFrom: 'znmx',
          })
        } catch {
          ElMessage.error('保存记录失败')
        }
      }
    }

    // 生成错误高亮map
    const inputArr = writingInput.value.trim().split('')
    const answerArr = currentWord.value.spelling.split('')
    const errorArr: boolean[] = []
    for (let i = 0; i < answerArr.length; i++) {
      errorArr.push(inputArr[i]?.toLowerCase() !== answerArr[i].toLowerCase())
    }
    writingErrorMap.value = errorArr
  }

  writingReviewOnceAttempt.value = false
}

// 默写阶段：下一个单词
const nextWritingWord = () => {
  if (currentPhase.value === 'writing') {
    if (writingIndex.value < totalWords.value - 1) {
      writingIndex.value++
      writingInput.value = ''
      writingError.value = false
      writingErrorMap.value = []
      writingShowSpelling.value = false
      writingReadyForNext.value = false
      focusWritingInput()
    } else {
      // 默写学习阶段完成，检查是否需要默写复习
      if (writingFailedWordIds.value.size > 0) {
        enterWritingReviewPhase()
      } else {
        finishWriting()
      }
    }
  } else if (currentPhase.value === 'writingReview') {
    // 判断如果完成默写复习的单词id集合个数等于需要默写复习的单词id集合个数，则进入结束阶段
    if (
      writingReviewCompletedWordIds.value.size ===
      writingFailedWordIds.value.size
    ) {
      // 默写复习完成
      finishWriting()
      return
    }

    // 筛选出在需要默写复习的单词id集合的，但是不在完成默写复习的单词id集合的单词id
    const remainingWordIds = new Set<number>()
    writingFailedWordIds.value.forEach((wordId) => {
      // 检查这个单词是否还没有被正确拼写过
      if (!writingReviewCompletedWordIds.value.has(wordId)) {
        remainingWordIds.add(wordId)
      }
    })

    // 随机选出一个单词id
    const remainingWordIdsArray = Array.from(remainingWordIds)
    const randomIndex = Math.floor(Math.random() * remainingWordIdsArray.length)
    const selectedWordId = remainingWordIdsArray[randomIndex]

    // 从需要默写复习的单词列表中选出该单词，作为下一个需要默写复习的单词
    const selectedWordIndex = writingReviewWords.value.findIndex(
      (word) => Number(word.id) === selectedWordId,
    )

    if (selectedWordIndex !== -1) {
      writingReviewIndex.value = selectedWordIndex
      writingInput.value = ''
      writingError.value = false
      writingErrorMap.value = []
      writingShowSpelling.value = false
      writingReadyForNext.value = false
      focusWritingInput()
      writingReviewOnceAttempt.value = true
    } else {
      // 如果找不到对应的单词，完成学习
      finishWriting()
    }
  }
}

// 进入默写复习阶段
const enterWritingReviewPhase = () => {
  currentPhase.value = 'writingReview'
  writingReviewWords.value = wordList.value.filter((word) =>
    writingFailedWordIds.value.has(Number(word.id)),
  )
  writingReviewIndex.value = 0
  writingReviewCorrectCountRef.value = 0 // Reset correct count for review phase
  writingReviewCompletedWordIds.value.clear() // Reset completed word ids
  writingInput.value = ''
  writingError.value = false
  writingErrorMap.value = []
  writingShowSpelling.value = false
  writingReadyForNext.value = false

  focusWritingInput()
}

// 完成默写
const finishWriting = async () => {
  currentPhase.value = 'finished'

  const score = Math.round((writingCorrectCount.value / totalWords.value) * 100)

  try {
    await unitStore.updateUnitStudyStatus({
      unitId: Number(currentUnit.value?.unitId) || 0,
      source: 2,
      writingCompleted: 1,
      writingScore: score,
      writingTimeConsuming: totalStudyTime.value,
    })
    await unitStore.updateUnitStudyStats({
      unitId: Number(currentUnit.value?.unitId) || 0,
      unitTotal: totalWords.value,
      studyNumZnmx: writingCorrectCount.value,
      testTimeZnmx: totalStudyTime.value,
      restudyNumZnmx: writingErrorCount.value,
      isEndZnmx: 1,
    })

    ElMessage.success(`默写完成！得分：${score}分`)
    router.push('/work/word-training')
  } catch {
    ElMessage.error('保存学习状态失败')
  }
}

// 时间相关函数
const formatSeconds = (s: number) => {
  const h = Math.floor(s / 3600)
  const m = Math.floor((s % 3600) / 60)
  const sec = s % 60
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`
}

const startSecondTimer = () => {
  if (secondTimer.value) clearInterval(secondTimer.value)
  secondTimer.value = window.setInterval(() => {
    if (!isPaused.value) {
      effectiveSeconds.value++
      // 每60秒上报一次有效时长
      if (effectiveSeconds.value % 60 === 0) {
        reportEffectiveSeconds()
      }
    }
  }, 1000)
}

const stopSecondTimer = () => {
  if (secondTimer.value) clearInterval(secondTimer.value)
}

// 上报有效时长API
const reportEffectiveSeconds = async () => {
  try {
    await DAILY_TIME_RECORDS_API.addOnlineOrEffectiveSeconds({
      effectiveSeconds: 60,
      onlineSeconds: 0,
    })
    // 上报后查一次最新有效时长
    const res = await DAILY_TIME_RECORDS_API.getTodayOnlineSeconds()
    if (res?.effectiveSeconds != null) {
      effectiveSeconds.value = res.effectiveSeconds
    }
  } catch {
    /* ignore */
  }
}

const startTotalStudyTimer = () => {
  if (totalStudyTimer.value) clearInterval(totalStudyTimer.value)
  totalStudyTimer.value = window.setInterval(() => {
    totalStudyTime.value++
  }, 1000)
}

const stopTotalStudyTimer = () => {
  if (totalStudyTimer.value) clearInterval(totalStudyTimer.value)
}

// 组件挂载
onMounted(async () => {
  try {
    const unit = await unitStore.getCurrentUnit()
    if (!unit?.unitId) {
      ElMessage.error('未找到当前单元信息')
      return
    }

    await writingStore.fetchWritingWords(unit.unitId)
    wordList.value = writingStore.wordList

    if (wordList.value.length === 0) {
      ElMessage.warning('当前单元没有单词')
      return
    }

    // 启动暂停有效时间计算
    addActivityListeners()
    startIdleTimer()
    startSecondTimer() // 启动每秒自增
    startTotalStudyTimer() // 启动总时间计时器
    window.addEventListener('keydown', handleKeydown)

    // 仅首次进入时获取今日有效时长
    try {
      const res = await DAILY_TIME_RECORDS_API.getTodayOnlineSeconds()
      if (res?.effectiveSeconds != null) {
        effectiveSeconds.value = res.effectiveSeconds
      }
    } catch {
      /* ignore */
    }

    // 聚焦输入框
    focusWritingInput()
  } catch {
    ElMessage.error('加载单词失败')
  }
})

// 组件卸载
onUnmounted(() => {
  removeActivityListeners()
  stopIdleTimer()
  stopSecondTimer() // 停止每秒自增
  stopTotalStudyTimer() // 停止总时间计时器
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.word-training-outer {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-bg-color-page);
}

.main-card {
  width: 66vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

.word-training {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}

.word-training__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .unit-name {
    font-size: 16px;
    color: var(--el-text-color-secondary);
  }
}

.word-training__title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.word-training__content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.word-training__footer {
  margin-top: 24px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .progress-info {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: var(--el-text-color-regular);

    .writing {
      color: #67c23a; // 默写学习阶段 - 绿色
    }

    .writing-review {
      color: #95d475; // 默写复习阶段 - 浅绿色
    }
  }
}

.word-card {
  width: 100%;
  max-width: 600px;
  padding: 32px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  &__meaning {
    text-align: center;
    margin-bottom: 24px;

    .meaning-content {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .part-of-speech {
      color: var(--el-text-color-secondary);
      margin-right: 8px;
    }
  }

  &__example {
    margin-top: 16px;
    text-align: center;

    .example-en {
      font-size: 16px;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }

    .example-cn {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }

  &__progress {
    // display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;

    .progress-text {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }
}
</style>
