# 单词本功能实现说明

## 功能概述

根据需求，完成了单词本功能的完整实现，包括API、Store、View三个模块，支持以下功能：

1. **教材选择**：支持选择不同的教材program
2. **单元选择**：支持选择特定单元或查看全部单元的单词
3. **分页表格显示**：以表格形式展示单词列表，包含序号、音标/读音、单词拼写、词义四列
4. **交互式显示控制**：支持点击单元格显示/隐藏单词拼写和词义
5. **全局显示控制**：支持全局切换显示/隐藏拼写和词义
6. **音频播放**：支持播放单词读音

## 文件结构

### 1. API模块
**文件位置**: `src/api/dcyx/word-book.api.ts`

**主要接口**:
- `getWordList`: 获取单词列表（按教材和单元）
- `getWordAudio`: 获取单词音频
- `getProgramList`: 获取教材列表
- `getUnitList`: 获取单元列表

**数据类型**:
- `WordBookItem`: 单词本单词项
- `ProgramItem`: 教材项
- `UnitItem`: 单元项
- `WordDisplayState`: 单词显示控制状态

### 2. Store模块
**文件位置**: `src/store/modules/dcyx_word_book.store.ts`

**主要功能**:
- 状态管理：单词列表、教材列表、单元列表等
- API调用封装：使用Promise包装，统一错误处理
- 音频播放：封装音频播放逻辑
- 数据缓存：本地状态保存，减少重复请求

**主要方法**:
- `fetchWordList`: 获取单词列表
- `fetchProgramList`: 获取教材列表
- `fetchUnitList`: 获取单元列表
- `playWordAudio`: 播放单词音频
- `resetWordBookStore`: 重置状态

### 3. View模块
**文件位置**: `src/views/dcyx/work-main/word-book.vue`

**页面布局**:
- **顶部栏**: 教材选择器 + 单元选择器 + 全局显示控制按钮
- **表格内容区**: 分页表格显示单词列表

**表格列设计**:
1. **序号列**: 显示单词在当前页的序号
2. **音标/读音列**: 显示音标和播放按钮
3. **单词拼写列**: 可点击显示/隐藏拼写，带切换按钮
4. **词义列**: 可点击显示/隐藏词义和例句，带切换按钮

**主要功能**:
- 教材和单元切换：支持选择不同教材和单元，自动刷新数据
- 交互式显示控制：点击单元格切换显示状态
- 全局显示控制：一键显示/隐藏所有单词的拼写或词义
- 音频播放：点击播放按钮播放单词读音
- 分页支持：使用统一的分页组件

## 代码规范遵循

### 1. API模块规范
- ✅ 完整的数据对象结构定义
- ✅ export interface导出结构
- ✅ API函数输入输出指定对象结构类型

### 2. Store模块规范  
- ✅ 使用new Promise风格包裹API调用
- ✅ 输入输出指定对象结构类型
- ✅ 单一职责，每个函数只调用一次API

### 3. View模块规范
- ✅ 使用Store和Composable模块函数
- ✅ 外部使用try-catch风格捕获错误
- ✅ 使用本地变量保存数据
- ✅ 使用组件模块（Pagination等）

### 4. Vue3最佳实践
- ✅ 使用Composition API
- ✅ 响应式数据管理
- ✅ 合理的组件拆分
- ✅ 统一的错误处理
- ✅ 类型安全的TypeScript

## 功能特点

### 1. 智能显示控制
- **个性化控制**: 每个单词可独立控制显示状态
- **全局控制**: 支持一键显示/隐藏所有单词的拼写或词义
- **状态同步**: 全局控制会同步更新所有单词的显示状态

### 2. 用户体验优化
- **点击交互**: 点击单元格即可切换显示状态
- **悬停效果**: 鼠标悬停显示切换按钮
- **视觉反馈**: 不同状态有不同的视觉样式
- **音频播放**: 支持单词读音播放

### 3. 数据管理
- **分页加载**: 支持大量单词的分页显示
- **序号管理**: 自动计算并显示单词序号
- **状态持久化**: 显示状态在页面切换时保持

### 4. 灵活筛选
- **教材筛选**: 可选择不同教材查看单词
- **单元筛选**: 可选择特定单元或查看全部单元
- **动态加载**: 选择变化时自动加载对应数据

## 使用说明

1. 访问单词本页面：`/work/word-book`
2. 选择教材后自动加载该教材的单元列表
3. 选择单元（可选）查看特定单元的单词
4. 使用全局按钮控制所有单词的显示状态
5. 点击单词拼写或词义单元格切换个别单词的显示状态
6. 点击播放按钮听取单词读音
7. 使用分页控件浏览更多单词

## 技术实现亮点

1. **状态管理**: 使用响应式数据管理每个单词的显示状态
2. **音频处理**: 封装音频播放逻辑，支持错误处理和资源清理
3. **交互设计**: 点击和悬停效果提升用户体验
4. **数据流**: 清晰的API → Store → View数据流
5. **类型安全**: 全程TypeScript类型检查

## 注意事项

1. 需要确保后端API接口已实现
2. 需要用户已登录并选择了教材系列
3. 音频播放需要浏览器支持Audio API
4. 显示状态在页面刷新后会重置为默认状态
