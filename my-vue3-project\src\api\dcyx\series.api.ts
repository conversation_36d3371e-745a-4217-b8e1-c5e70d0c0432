import request from '@/utils/request'

const SERIES_BASE_URL = '/api/client/v1'

const DCYX_SERIES_API = {
  getSemesterList: (params: PageQuery) => {
    return request<any, PageResult<Semester[]>>({
      url: `${SERIES_BASE_URL}/semester/page`,
      method: 'get',
      params,
    })
  },

  getSeriesBySemesterId: (params: SeriesQueryParams) => {
    return request<any, PageResult<Series[]>>({
      url: `${SERIES_BASE_URL}/series/page`,
      method: 'get',
      params,
    })
  },
}
export default DCYX_SERIES_API

/**
 * 			{
				"id": 0,
				"dispOrder": 0,
				"nameEnUs": "",
				"nameZhCn": "",
				"nameZhBig": "",
				"type": 0,
				"showFree": 0,
				"content": "",
				"status": 0,
				"picture": ""
			}
 */
export interface Semester {
  id: number
  dispOrder: number
  nameEnUs: string
  nameZhCn: string
  nameZhBig: string
  type: number
  showFree: number
  content: string
  status: number
  picture: string
}

/**
 * 			{
				"id": 0,
				"semesterId": 0,
				"dispOrder": 0,
				"nameEnUs": "",
				"nameZhCn": "",
				"nameZhBig": "",
				"type": 0,
				"showFree": 0,
				"content": "",
				"createUser": 0,
				"createTime": "",
				"status": 0,
				"picture": ""
			}
 */

export interface Series {
  id: number
  semesterId: number
  dispOrder: number
  nameEnUs: string
  nameZhCn: string
  nameZhBig: string
  type: number
  showFree: number
  content: string
  createUser: number
  createTime: string
  updateTime: string
  updateUser: string
  status: number
  picture: string
}
