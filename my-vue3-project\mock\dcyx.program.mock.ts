// mock/program.ts
// import { MockMethod } from 'vite-plugin-mock'
import { defineMock } from './base'

export default defineMock([
  {
    url: 'program/getProgramBySeriesId/:id',
    method: ['GET'],
    body: ({ params }) => {
      const id = params.id
      console.log('seriesId:', id)
      return {
        code: '00000',
        msg: '一切ok',
        data: [
          {
            id: '9',
            seriesId: '5',
            name: 'CN-GaoZhong11',
            totalWords: '616',
            expLanguage: 'Simplified-Chinese',
            nameEnUs: '',
            nameZhCn: '一年级上',
            nameZhBig: '',
            disporder: '60',
            remark: null,
            type: '1',
            createUser: '1',
            createTime: '2024-11-02 21:42:13',
            updateTime: '2024-11-02 21:42:13',
            updateUser: '1',
            status: '1',
            studyNumber: '0',
            percent: 0.0,
            percentStr: null,
          },
          {
            id: '10',
            seriesId: '5',
            name: 'CN-GaoZhong12',
            totalWords: '542',
            expLanguage: 'Simplified-Chinese',
            nameEnUs: '',
            nameZhCn: '一年级下',
            nameZhBig: '',
            disporder: '70',
            remark: null,
            type: '1',
            createUser: '1',
            createTime: '2024-11-02 21:42:13',
            updateTime: '2024-11-02 21:42:13',
            updateUser: '1',
            status: '1',
            studyNumber: '0',
            percent: 0.0,
            percentStr: null,
          },
          {
            id: '11',
            seriesId: '5',
            name: 'CN-GaoZhong21',
            totalWords: '562',
            expLanguage: 'Simplified-Chinese',
            nameEnUs: '',
            nameZhCn: '二年级上',
            nameZhBig: '',
            disporder: '80',
            remark: null,
            type: '1',
            createUser: '1',
            createTime: '2024-11-02 21:42:13',
            updateTime: '2024-11-02 21:42:13',
            updateUser: '1',
            status: '1',
            studyNumber: '0',
            percent: 0.0,
            percentStr: null,
          },
          {
            id: '12',
            seriesId: '5',
            name: 'CN-GaoZhong22',
            totalWords: '508',
            expLanguage: 'Simplified-Chinese',
            nameEnUs: '',
            nameZhCn: '二年级下',
            nameZhBig: '',
            disporder: '90',
            remark: null,
            type: '1',
            createUser: '1',
            createTime: '2024-11-02 21:42:13',
            updateTime: '2024-11-02 21:42:13',
            updateUser: '1',
            status: '1',
            studyNumber: '0',
            percent: 0.0,
            percentStr: null,
          },
          {
            id: '13',
            seriesId: '5',
            name: 'CN-GaoZhong30',
            totalWords: '833',
            expLanguage: 'Simplified-Chinese',
            nameEnUs: '',
            nameZhCn: '三年级',
            nameZhBig: '',
            disporder: '95',
            remark: null,
            type: '1',
            createUser: '1',
            createTime: '2024-11-02 21:42:13',
            updateTime: '2024-11-02 21:42:13',
            updateUser: '1',
            status: '1',
            studyNumber: '0',
            percent: 0.0,
            percentStr: null,
          },
        ],
        success: true,
        fail: false,
      }
    },
  },
  {
    url: 'program/unit/getUnitsByProgram/:seriesid/:programid',
    method: ['GET'],
    body: ({ params }) => {
      const seriesid = params.seriesid
      const programid = params.programid
      console.log('seriesid:', seriesid)
      console.log('programid:', programid)
      return {
        code: '00000',
        msg: '一切ok',
        data: [
          {
            id: '392',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit11',
            nameEnUs: 'Unit11',
            nameZhCn: 'Unit11',
            nameZhBig: null,
            unitIndex: '0',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '54',
          },
          {
            id: '393',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit12',
            nameEnUs: 'Unit12',
            nameZhCn: 'Unit12',
            nameZhBig: null,
            unitIndex: '1',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '52',
          },
          {
            id: '394',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit13',
            nameEnUs: 'Unit13',
            nameZhCn: 'Unit13',
            nameZhBig: null,
            unitIndex: '2',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '55',
          },
          {
            id: '395',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit14',
            nameEnUs: 'Unit14',
            nameZhCn: 'Unit14',
            nameZhBig: null,
            unitIndex: '3',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '53',
          },
          {
            id: '396',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit15',
            nameEnUs: 'Unit15',
            nameZhCn: 'Unit15',
            nameZhBig: null,
            unitIndex: '4',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '44',
          },
          {
            id: '397',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit16',
            nameEnUs: 'Unit16',
            nameZhCn: 'Unit16',
            nameZhBig: null,
            unitIndex: '5',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '55',
          },
          {
            id: '398',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit17',
            nameEnUs: 'Unit17',
            nameZhCn: 'Unit17',
            nameZhBig: null,
            unitIndex: '6',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '38',
          },
          {
            id: '399',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit18',
            nameEnUs: 'Unit18',
            nameZhCn: 'Unit18',
            nameZhBig: null,
            unitIndex: '7',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '51',
          },
          {
            id: '400',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit19',
            nameEnUs: 'Unit19',
            nameZhCn: 'Unit19',
            nameZhBig: null,
            unitIndex: '8',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '58',
          },
          {
            id: '401',
            seriesId: '5',
            programId: '12',
            programName: 'CN-GaoZhong22',
            name: 'Unit20',
            nameEnUs: 'Unit20',
            nameZhCn: 'Unit20',
            nameZhBig: null,
            unitIndex: '9',
            createUser: '1',
            createTime: '2024-11-02 21:52:20',
            updateTime: '2024-11-02 21:52:20',
            updateUser: '1',
            status: '1',
            totalNUm: '48',
          },
        ],
        success: true,
        fail: false,
      }
    },
  },
  {
    url: 'program/unit/getUnitsInfoByProgram/:parentid/:seriesid/:programid',
    method: ['GET'],
    body: ({ params }) => {
      const parentid = params.parentid
      const seriesid = params.seriesid
      const programid = params.programid
      console.log('parentid:', parentid)
      console.log('seriesid:', seriesid)
      console.log('programid:', programid)
      return {
        code: '00000',
        msg: '一切ok',
        data: [
          {
            id: '15848',
            name: 'Starter Unit1',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15849',
            name: 'Starter Unit2',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15850',
            name: 'Starter Unit3',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15851',
            name: 'Unit1（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15852',
            name: 'Unit1（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15853',
            name: 'Unit2（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15854',
            name: 'Unit2（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15855',
            name: 'Unit3（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15856',
            name: 'Unit3（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15857',
            name: 'Unit4（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15858',
            name: 'Unit4（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15859',
            name: 'Unit5（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15860',
            name: 'Unit5（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15861',
            name: 'Unit6（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15862',
            name: 'Unit6（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15863',
            name: 'Unit7（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15864',
            name: 'Unit7（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15865',
            name: 'Unit8（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15866',
            name: 'Unit8（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15867',
            name: 'Unit9（1）',
            score: null,
            time: null,
            isEnd: '0',
          },
          {
            id: '15868',
            name: 'Unit9（2）',
            score: null,
            time: null,
            isEnd: '0',
          },
        ],
        success: true,
        fail: false,
      }
    },
  },
  {
    url: 'program/word/countWordsByUnit/:seriesid/:programid/:unit_name',
    method: ['GET'],
    body: ({ params }) => {
      const seriesid = params.seriesid
      const programid = params.programid
      const unit_name = params.unit_name
      console.log('seriesid:', seriesid)
      console.log('programid:', programid)
      console.log('unit_name:', unit_name)
      return {
        code: '00000',
        msg: '一切ok',
        data: {
          unitTotal: '54',
          studyNumForZnjy: 0.0,
          studyNumForZnmx: '0',
          studyNumForZntx: '0',
          testTimeForZnjy: '0',
          testTimeForZnmx: '0',
          testTimeForZntx: '0',
          restudyNumForZnjy: '0',
          restudyNumForZnmx: '0',
          restudyNumForZntx: '0',
          isEndZnjy: '0',
          isEndZntx: '0',
          isEndZnmx: '0',
        },
        success: true,
        fail: false,
      }
    },
  },
])
